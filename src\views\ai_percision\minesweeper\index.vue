<template>
  <div class="graph-content">
    <div class="left" v-loading="loading">
      <knowledgeTree 
        :selected="chapterId" 
        :iswen="true" 
        :options="options" 
        :showCurrentTaskIndicator="!!routeHasChapterId"
        currentTaskImageUrl="@/assets/img/synchronous/task-badge.png"
        @setChapterId="setChapterId" 
      />
      <div class="title-coustom">
        选择章节
        <span></span>
      </div>
    </div>
    <div class="right" v-loading="loading2">
      <div v-if="pageStatus">
        <div class="right-item bg1">
          <div class="right-item-title">
            <img src="@/assets/img/percision/foundation.png" > 基础巩固
          </div>
          <div class="right-item-degree">
            难度：<el-rate v-model="chapterData.rate1" :max="3" disabled />
          </div>
          <div class="right-item-mastery">
            <div class="right-item-mastery-text" v-if="chapterData.percentage1 != null && chapterData.percentage1 > 0">掌握度：<span class="green">{{ chapterData.percentage1i }}%</span></div>
            <div class="right-item-mastery-text" v-else>待练习</div>
            <el-progress :percentage="chapterData.percentage1" :show-text="false" status="success" />
          </div>
          <div class="right-item-btn" :class="chapterData.percentage1 > 90?'grey': ''" @click="toPractice(chapterData.percentage1 == -1?1:4)">
            <img :src="getUrl(chapterData.percentage1)" > {{chapterData.percentage1 > 90?"已巩固":"去练习"}}
          </div>
          <img class="strong-img" v-show="chapterData.percentage1 > 90" src="@/assets/img/percision/strong-img.png" />
        </div>
        <div class="right-item bg2">
          <div class="right-item-title light-red">
            <img src="@/assets/img/percision/advanced.png" > 综合进阶
          </div>
          <div class="right-item-degree">
            难度：<el-rate v-model="chapterData.rate2" :max="3" disabled />
          </div>
          <div class="right-item-mastery">
            <div class="right-item-mastery-text" v-if="chapterData.percentage2 != null && chapterData.percentage2 > 0">掌握度：<span class="orange">{{ chapterData.percentage2i }}%</span></div>
            <div class="right-item-mastery-text" v-else>待练习</div>
            <el-progress :percentage="chapterData.percentage2" :show-text="false" status="warning" />
          </div>
          <div class="right-item-btn" :class="chapterData.percentage2 > 90?'grey': ''" @click="toPractice(chapterData.percentage1 == -1?2:5)">
            <img :src="getUrl(chapterData.percentage2)" >  {{chapterData.percentage2 > 90?"已巩固":"去练习"}}
          </div>
          <img class="strong-img" v-show="chapterData.percentage2 > 90" src="@/assets/img/percision/strong-img.png" />
        </div>
        <div class="right-item bg3">
          <div class="right-item-title popul">
            <img src="@/assets/img/percision/difficult-points.png" > 难点突破
          </div>
          <div class="right-item-degree">
            难度：<el-rate v-model="chapterData.rate3" :max="3" disabled />
          </div>
          <div class="right-item-mastery">
            <div class="right-item-mastery-text" v-if="chapterData.percentage3 != null && chapterData.percentage3 > 0">掌握度：<span class="red">{{ chapterData.percentage3i }}%</span></div>
            <div class="right-item-mastery-text" v-else>待练习</div>
            <el-progress :percentage="chapterData.percentage3" :show-text="false" status="exception" />
          </div>
          <div class="right-item-btn" :class="chapterData.percentage3 > 90?'grey': ''" @click="toPractice(chapterData.percentage1 == -1?3:6)">
            <img :src="getUrl(chapterData.percentage3)" > {{chapterData.percentage3 > 90?"已巩固":"去练习"}}
          </div>
          <img class="strong-img" v-show="chapterData.percentage3 > 90" src="@/assets/img/percision/strong-img.png" />
        </div>
      </div>
      <div v-else class="right-item bg1">
        <div class="right-item-title">
          <img src="@/assets/img/percision/unit-test.png" > 单元测试
        </div>
        <div class="right-item-degree">
          难度：<el-rate v-model="chapterData.rate3" :max="3" disabled />
        </div>
        <div class="right-item-mastery">
          <div class="right-item-mastery-text" v-if="chapterData.percentage0 != null && chapterData.percentage0 > 0">掌握度：<span class="red">{{ chapterData.percentage0i }}%</span></div>
          <div class="right-item-mastery-text" v-else>待练习</div>
          <el-progress :percentage="chapterData.percentage0" :show-text="false" status="exception" />
        </div>
        <div class="right-item-btn" :class="chapterData.percentage0 > 90?'grey': ''" @click="toPractice(0)">
          <img :src="getUrl(chapterData.percentage0)" > {{chapterData.percentage0 > 90?"已巩固":"去练习"}}
        </div>
        <img class="strong-img" v-show="chapterData.percentage0 > 90" src="@/assets/img/percision/strong-img.png" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter ,useRoute} from 'vue-router'
import knowledgeTree from "@/views/components/knowledgeTree/index.vue"
import { getBookChapterListApi, getMasteryApi } from "@/api/book"
import { computed, nextTick, onMounted, reactive, ref } from 'vue'
import { useUserStore } from "@/store/modules/user"
import { dataEncrypt, dataDecrypt } from "@/utils/secret"
import { storeToRefs } from 'pinia'
const userStore = useUserStore()

const { subjectObj, learnNow } = storeToRefs(userStore)

const router = useRouter()
const route = useRoute()
const query = reactive<any>(route.query)
const loading = ref(false)
const loading2 = ref(false)
const pageStatus = ref(true)
const chapterId = ref("")
const routeHasChapterId = ref(false) // 标记是否从路由获取了章节ID
const isTargetChapterId = ref()
const chapterData = reactive<any>({
  percentage1: null,
  percentage1i: null,
  strong1: false,
  rate1: 1,
  percentage2: null,
  percentage2i: null,
  strong2: false,
  rate2: 2,
  percentage3: null,
  percentage3i: null,
  strong3: false,
  rate3: 3,
  percentage0: null,
  percentage0i: null,
  strong0: false,
})
const options = ref([])

const getUrl = (item: any) => {
    let url = 'pen'
    if (item > 90) {
        url = 'strong'
    }
    return new URL(`../../../assets/img/percision/${url}.png`, import.meta.url).href //静态资源引入为url，相当于require()
}
// 递归查找章节，并返回包含该章节的完整路径
const findChapterById = (chapters, targetId, path = []) => {
  let deepestPath: any[] | null = null;
  
  for (const chapter of chapters) {
    // 创建当前路径的副本
    const currentPath:any = [...path, chapter]
    
    // 如果找到目标章节，记录当前路径
    if (chapter.id === targetId) {
      // 记录找到的路径，但不立即返回
      deepestPath = currentPath;
    }
    
    // 无论是否已找到匹配节点，都继续递归查找子节点
    if (chapter.children && chapter.children.length > 0) {
      const childResult: any[] | null = findChapterById(chapter.children, targetId, currentPath)
      // 如果子节点中找到了结果，优先使用子节点的结果（更深层次）
      if (childResult) {
        // 如果已经有记录但找到了更深的路径，或者还没有记录
        if (!deepestPath || childResult.length > deepestPath.length) {
          deepestPath = childResult;
        }
      }
    }
  }
  
  return deepestPath;
}

onMounted(async () => {
  const chapters = await getBookChapterList()
  console.log(query.contentType   ,"query.contentType   "  )
  // 从路由参数中获取章节ID
  let targetChapterId = null
  
  if (query.chapterId) {
    console.log(query.chapterId,"query.chapterIdquery.chapterId")
    targetChapterId = query.chapterId
  } else if (query.data) {
    try {
      // 尝试解密data参数
      const decryptedData = dataDecrypt(query.data)
      if (decryptedData && decryptedData.chapterId) {
        targetChapterId = decryptedData.chapterId
      }
    } catch (error) {
      console.error('Failed to decrypt query data:', error)
    }
  }
  
  // 如果找到目标章节ID，设置选中状态并获取掌握度
  if (targetChapterId) {
    chapterId.value = targetChapterId
    // 标记章节ID来自路由
    routeHasChapterId.value = true
    
    // 查找章节路径，可用于展开树形结构
    const chapterPath = findChapterById(chapters, targetChapterId)
    console.log("targetChapterId",targetChapterId)

    if (chapterPath && chapterPath.length > 0) {
      // 获取最后一个节点（目标章节）的名称
      const targetChapter = chapterPath[chapterPath.length - 1]
      // 设置页面状态（是否为单元测试）
      pageStatus.value = targetChapter.name !== "单元测试"
    }
    
    // 获取掌握度数据
    getMastery()
  }
})

//获取章节列表
const getBookChapterList = async() => {
  if(query.contentType == 'historyTask'){
    subjectObj.value.bookId = query.bookId
  }
  loading.value = true
  loading2.value = true
  try {
    const res: any = await getBookChapterListApi({
      bookId:subjectObj.value.bookId,
      hierarchy: 3,
      type: 1
    })
    if(res.code == 200) {
      options.value = res.data || []
      // 只在没有路由参数时设置默认章节
      if (!query.chapterId && !query.data) {
        chapterId.value = getLast(res.data[0]) || ""
        getMastery()
      }
    }
    loading.value = false
    return res.data || []
  } catch (error) {
    options.value = []
    console.log(error)
    loading.value = false
    return []
  }
}
const getLast = (data: any) => {
  let id = ""
  if(data.children.length > 0) {
    return getLast(data.children[0])
  } else {
    id = data.id
    return id
  }
}
const setChapterId = (data: any, name: string) => {
  pageStatus.value = data.name != "单元测试"
  chapterId.value = data.id
  
  // 当用户手动选择章节时，重置标记
  routeHasChapterId.value = false
  
  // 更新页面数据
  getMastery()
  
  // 可选：更新URL，保持状态同步（不刷新页面）
  router.replace({
    query: {
      ...route.query,
      chapterId: data.id
    }
  })
}
const getMastery = async() => {
  loading2.value = true
  try {
    const res: any = await getMasteryApi({
      bookId: subjectObj.value.bookId,
      chapterId: chapterId.value
    })
    if(res.data) {
      res.data[0].trainCollect.map((item:any) => {
        let num = Number(item.mastery)
        if (item.type == 1) {
          chapterData.percentage1 = num
          chapterData.percentage1i = parseFloat((num).toFixed(2))
        } else if (item.type == 2) {
          chapterData.percentage2 = num
          chapterData.percentage2i = parseFloat((num).toFixed(2))
        } else if (item.type == 3) {
          chapterData.percentage3 = num
          chapterData.percentage3i = parseFloat((num).toFixed(2))
        } else if (item.type == 0) {
          chapterData.percentage0 = num
          chapterData.percentage0i = parseFloat((num).toFixed(2))
        }
      })
    } else {
      chapterData.percentage1 = 0
      chapterData.percentage1i = 0
      chapterData.percentage2 = 0
      chapterData.percentage2i = 0
      chapterData.percentage3 = 0
      chapterData.percentage3i = 0
      chapterData.percentage0 = 0
      chapterData.percentage0i = 0
    }
    loading2.value = false
  } catch (error) {
    chapterData.value = {}
    loading2.value = false
  }
}
const toPractice = (chapterTrainType) => {
  //chapterTrainType 基础巩固1待训练，第二次做传4;综合进阶2,5；难点突破3,6
  router.push({
    path: '/ai_percision/minesweeper/paper_write_switchM',
    query: { 
      data: dataEncrypt({
        chapterId: chapterId.value,
        chapterTrainType,
        pageSource: '4',
        contentType:query.contentType       
      })
    }
  })
}

</script>
<style scoped lang="scss">
.graph-content {
  display: flex;
}
.left {
  position: relative;
  .title-coustom {
    width: 11.1875rem;
    height: 2.875rem;
    line-height: 2.875rem;
    text-align: center;
    border-bottom-right-radius: .625rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 700;
    position: absolute;
    left: -0.875rem;
    top: .625rem;
    span{
      display: inline-block;
      border-top: .4375rem solid #00886E;
      border-left: .4375rem solid #F5F5F5;
      border-bottom: .4375rem solid #F5F5F5;
      border-right: .4375rem solid #00886E;
      position: absolute;
      bottom: -0.875rem;
      left: 0;
      
    }
  }
}
.right {
  margin-left: .625rem;
  width: 60.0625rem;
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
  border-radius: 1.25rem 0 0 1.25rem;
  overflow-y: auto;
  box-sizing: border-box;
  background-color: white;
  padding: 1.25rem;
  .bg1 {
    background-image: url(@/assets/img/percision/strong-bg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .bg2 {
    background-image: url(@/assets/img/percision/advanced-bg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .bg3 {
    background-image: url(@/assets/img/percision/difficult-bg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  &-item {
    background: #eef3fd;
    height: 13rem;
    padding: .75rem 1.375rem;
    position: relative;
    border-radius: .625rem;
    margin-bottom: 1.25rem;
    .strong-img {
      position: absolute;
      right: 0;
      top: 0;
      width: 8.5625rem;
      height: 6.9375rem;
    }
    &-title {
      display: flex;
      align-items: center;
      color: #5a85ec;
      font-size: 1.25rem;
      font-weight: 700;
      margin-bottom: 1.25rem;
      img {
        width: 1.375rem;
        height: 1.375rem;
        margin-right: .5rem;
      }
    }
    &-degree {
      color: #2a2b2a;
      font-size: .875rem;
      font-weight: 400;
      margin-bottom: 1.625rem;
      display: flex;
      align-items: center;
    }
    &-mastery {
      color: #2a2b2a;
      font-size: .875rem;
      font-weight: 400;
      margin-bottom: .75rem;
      &-text {
        margin-bottom: .375rem;
      }
      span {
        color: #009C7F;
        font-size: 1.25rem;
        font-weight: 700;
      }
    }
    &-btn {
      width: 7.625rem;
      height: 2.375rem;
      border-radius: 1.1875rem;
      background: linear-gradient(150.8deg, #36e2c2 0%, #00b7d0 100%);
      color: #ffffff;
      font-size: 1rem;
      font-weight: 400;
      display: flex;
      align-items: center;
      justify-content: center;
      float: right;
      cursor: pointer;
      img {
        margin-right: .5rem;
        width: 1rem;
      }
    }
    .orange {
      color: #EF9D19!important;
    }
    .red {
      color: #DD2A2A!important;
    }
    .grey {
      background: #f5f5f5!important;
      color: #999999!important;
    }
  }
}
.light-red {
  color: #ec5a5a;
}
.popul {
  color: #665aec;
}
:deep(.el-progress-bar__outer) {
  height: .625rem!important;
}
</style>