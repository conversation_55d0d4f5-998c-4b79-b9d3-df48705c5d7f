<template>
    <header class="page-header">
      <div class="breadcrumbs">
        <a href="#" class="back-link" @click.prevent="goBack">&lt; 返回</a>
        <span class="breadcrumb-separator">&nbsp;/&nbsp;</span>
        <span class="breadcrumb-item">督学</span>
        <span class="breadcrumb-separator">&nbsp;/&nbsp;</span>
        <span class="breadcrumb-item active">分析</span>
      </div>
    </header>
    <div class="container" v-loading="reportState.loading">
        <div class="top-box">
            <div class="title">
                <div class="title-box">
                    <img class="test-icon" src="@/assets/img/percision/title.png" />
                    <div class="test-title">{{ queryData.title }}</div>
                </div>
            </div>
            <div class="title-handle">
                <!-- <div class="btn" @click="share">分享给好友</div> -->
                <div class="title-data">
                    <div class="title-data-item">
                        <img class="test-icon" src="@/assets/img/percision/answer-num.png" />
                        <div class="test-title">答题数：<span>{{ reportState.quesCount }}</span></div>
                    </div>
                    <div class="title-data-item" v-if="reportState.topicScore !== '0.00'">
                        <img class="test-icon" src="@/assets/img/percision/score.png" />
                        <div class="test-title">得分：<span>{{ reportState.topicScore }}分</span></div>
                    </div>
                    <div class="title-data-item">
                        <img class="test-icon" src="@/assets/img/percision/correct-rate.png" />
                        <div class="test-title">正确率：<span>{{ reportState.correctRate }}%</span></div>
                    </div>
                    <div class="title-data-item">
                        <img class="test-icon" src="@/assets/img/percision/time.png" />
                        <div class="test-title">答题时长：<span>{{ reportState.answerTime }}</span></div>
                    </div>
                </div>
                <!-- <div class="green-underline" @click="goDetail">查看训练详情 ></div> -->
            </div>
        </div>
        <div class="middle-box">
            <div class="middle-table">
                <div class="table-title">
                    <span></span>答题情况
                </div>
                
                <!-- 添加答题情况表格 -->
                <div class="question-result-table">
                    <div class="result-table">
                        <div class="table-header">
                            <div class="header-row">
                                <div class="header-cell first-cell">题号</div>
                                <div v-for="(item, index) in pointData" :key="`header-${index}`" class="header-cell">
                                    {{ index + 1 }}
                                </div>
                            </div>
                        </div>
                        <div class="table-body">
                            <div class="body-row">
                                <div class="body-cell first-cell">答题情况</div>
                                <div v-for="(item, index) in pointData" :key="`body-${index}`" class="body-cell">
                                    <div class="status-icon" :class="getAnswerStatusClass(item)">
                                        <el-icon v-if="getAnswerStatusClass(item) === 'status-correct'"><Check /></el-icon>
                                        <el-icon v-else-if="getAnswerStatusClass(item) === 'status-wrong'"><Close /></el-icon>
                                        <el-icon v-else><WarningFilled /></el-icon>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="table-title">
                    <span></span>难易程度分析
                </div>
                <!-- {{ degreeData }} -->
                <el-table :data="degreeData" class="table">
                    <el-table-column prop="degreeType" label="试题难易程度">
                        <template #default="scope">
                            {{ getDegreeStatus(scope.row.degreeType) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="quesCount" label="题量">
                        <template #default="scope">
                            {{ scope.row.quesCount }} 
                        </template>
                    </el-table-column>
                    <el-table-column prop="quesRatio" label="题量占比">
                        <template #default="scope">
                            {{ formatCorrectRate(scope.row.quesRatio) }} %
                        </template>
                    </el-table-column>
                    <el-table-column prop="quesNos" label="题号" width="350" />
                </el-table>

                <!-- <div class="table-title">
                    <span></span>知识点分析
                </div> -->
              
                <!-- <el-table :data="pointData" class="table" height="18.75rem"> -->
                    <!-- <el-table-column prop="accuracy" label="掌握情况" width="210">
                        <template #default="scope">
                            <div class="center-box">
                                <div class="table-status" :class="getStatusClass(scope.row.status)">{{ getStatus(scope.row.status) }}</div>
                            </div>
                        </template>
                    </el-table-column> -->
                    
                    <!-- <el-table-column prop="promote" label="分值">
                        <template #default="scope">
                            {{ scope.row.score }}
                        </template>
                    </el-table-column> -->
                    <!-- <el-table-column prop="pointName" label="知识点名称" />
                    <el-table-column prop="promote" label="题数">
                        <template #default="scope">
                             {{ scope.row.score ?scope.row.score:1 }} 
                        </template>
                    </el-table-column>
                    <el-table-column prop="promote" label="正确率">
                        <template #default="scope">
                            {{ formatCorrectRate(scope.row.correctRate) }} %
                        </template>
                    </el-table-column>
                </el-table> -->

                <div class="recommond" v-if="pointVos.length">
                    <img src="@/assets/img/percision/recormond.png" alt="">
                    <p>学习一下下面几个知识点再次测试，你的成绩肯定可以提高不少哦。</p>
                </div>
                <div class="knowledge-video">
                    <div class="knowledge-item" v-for="(item, index) in pointVos" :key="index" :data-id="item.pointId"
                    :data-name="item.pointName" :data-i="index" @click="goWklist">
                        <img class="test-icon" src="@/assets/img/percision/play-blue.png" />
                        <div class="test-title">{{ item.pointName }}</div>
                    </div>
                </div>
            </div>
            
            <!-- 添加图表展示区域 -->
            <div class="charts-container">
                <div class="chart-box">
                    <div class="chart-title">题目难度</div>
                    <div class="chart" id="difficultyChart" ref="difficultyChartRef"></div>
                    <div class="chart-legends">
                        <div v-for="(item, index) in difficultyLegends" :key="index" class="legend-item">
                            <span class="legend-color" :style="{ backgroundColor: item.color }"></span>
                            <span class="legend-text">{{ item.name }}</span>
                            <span class="legend-count">题数: {{ item.count }}</span>
                        </div>
                    </div>
                </div>
                <div class="chart-box">
                    <div class="chart-title">答题情况</div>
                    <div class="chart" id="answerChart" ref="answerChartRef"></div>
                    <div class="chart-center-text">
                        <div class="correct-count">{{ reportState.correctCount }}</div>
                        <div class="total-count">/{{ reportState.quesCount }}</div>
                        <div class="text-label">答题情况</div>
                    </div>
                </div>
            </div>
        </div>
    </div>>
</template>

<script lang="ts" setup>
import { watch, onMounted, reactive, ref, onUnmounted } from 'vue'
import { getDetailsApi } from '@/api/training'
import { useUserStore } from "@/store/modules/user"
import { useRouter, useRoute } from 'vue-router'
import { createAppletCodeApi } from "@/api/user"
import { dataDecrypt, dataEncrypt, mergeObject } from "@/utils/secret"
import { storeToRefs } from 'pinia'

import { wenke } from '@/utils/user/enum'

import { assignmentsReportApi } from '@/api/online'

import { Check, Close, WarningFilled } from '@element-plus/icons-vue'
import * as echarts from 'echarts/core'
import { PieChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册必须的组件
echarts.use([TitleComponent, TooltipComponent, LegendComponent, PieChart, CanvasRenderer])

const route = useRoute()
const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
// const queryData = route.query.data ? route.query.data  : {}
const router = useRouter()
const reportState = reactive({
    switch: false,
    loading: false,
    showVip: false,
    correctRate: false,
    dialog: false,
    quesCount: 0,
    score: 0,
    correct: 0,
    title: "",
    trainTime: "",
    qrcode: "",
    topicScore:'',
    trainingId: "",
    answerTime:"",
    correctCount: 0, // 新增：用于存储答题情况图表的正确题数
})
const tableData = ref([
    { date: '2016-05-02', name: '王小虎', address: '上海市普陀区金沙江路 1518 弄' },
    { date: '2016-05-04', name: '王小虎', address: '上海市普陀区金沙江路 1517 弄' },
    { date: '2016-05-01', name: '王小虎', address: '上海市普陀区金沙江路 1519' }
])
const pointData = ref([])
const degreeData = ref([])
const pointVos = ref([] as any[])
const userStore = useUserStore()
const { subjectObj, learnNow } = storeToRefs(userStore)

// 图表相关
const difficultyChartRef = ref(null)
const answerChartRef = ref(null)
let difficultyChart = null
let answerChart = null
const difficultyLegends = ref([
  { name: '难', color: '#5470c6', count: 0 },
  { name: '较难', color: '#91cc75', count: 0 },
  { name: '中等', color: '#fac858', count: 0 },
  { name: '较易', color: '#ee6666', count: 0 },
  { name: '易', color: '#73c0de', count: 0 }
])

// 初始化难度分布图表
const initDifficultyChart = () => {
  if (!difficultyChartRef.value) return
  
  difficultyChart = echarts.init(difficultyChartRef.value)
  
  // 处理难度分布数据
  const difficultyMap = {
    1: '易',
    2: '较易',
    3: '中等',
    4: '较难',
    5: '难'
  }
  
  const colorMap = {
    '易': '#73c0de',
    '较易': '#91cc75',
    '中等': '#fac858',
    '较难': '#ee6666',
    '难': '#5470c6'
  }
  
  // 从degreeData中提取数据
  const seriesData = degreeData.value.map(item => {
    const name = difficultyMap[item.degreeType] || '未知'
    const value = item.quesCount
    const color = colorMap[name]
    
    // 更新图例数据
    const legendIndex = difficultyLegends.value.findIndex(legend => legend.name === name)
    if (legendIndex !== -1) {
      difficultyLegends.value[legendIndex].count = value
    }
    
    return { name, value, itemStyle: { color } }
  })
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '题目难度',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: seriesData
      }
    ]
  }
  
  difficultyChart.setOption(option)
}

// 初始化答题情况图表
const initAnswerChart = () => {
  if (!answerChartRef.value) return
  
  answerChart = echarts.init(answerChartRef.value)
  
  const correctCount = parseInt(reportState.correctCount) || 0
  const totalCount = reportState.quesCount || 0
  const wrongCount = totalCount - correctCount
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '答题情况',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { 
            value: correctCount, 
            name: '正确',
            itemStyle: { color: '#00c9a3' }
          },
          { 
            value: wrongCount, 
            name: '错误',
            itemStyle: { color: '#e9e9e9' }
          }
        ]
      }
    ]
  }
  
  answerChart.setOption(option)
}

// 窗口大小变化时重新调整图表大小
const resizeCharts = () => {
  difficultyChart?.resize()
  answerChart?.resize()
}

// 格式化正确率
const formatCorrectRate = (value: number) => {
  // 将小数转换为百分比值
  let percentage = value * 100;
  
  // 限制最大值为100%
  percentage = Math.min(percentage, 100);
  
  // 转换为保留2位小数的字符串
  const fixed = percentage.toFixed(2);
  
  // 如果小数点后都是0，则返回整数
  if (fixed.endsWith('.00')) {
    return Math.floor(percentage);
  }
  
  // 否则保留原始小数
  return Number(fixed);
}

const getStatus = (status: number) => {
    switch (status) {
        case 1:
            return "已掌握"
        case 2:
            return "一般"
        case 3:
            return "未掌握"
        default:
            return ""
    }
}
const getDegreeStatus = (status: number) => {
    switch (status) {
        case 1:
            return "易"
        case 2:
            return "较易"
        case 3:
            return "中档"
        case 4:
            return "较难"
        case 5:
            return "难"
        default:
            return "-"
    }
}
const getStatusClass = (status: number) => {
    switch (status) {
        case 1:
            return "green-box"
        case 2:
            return "yellow-box"
        case 3:
            return "red-box"
        default:
            return ""
    }
}
const getAnswerStatusClass = (item: any) => {
    // 判断是否正确
    const userAnswer = item.userJson ? item.userJson[0] : null;
    const correctAnswer = item.displayAnswer || '';
    
    // 将字母答案转为数字索引
    const letterToIndex = (letter: string) => {
        return letter.charCodeAt(0) - 65; // A=>0, B=>1, C=>2, D=>3...
    };
    
    // 比较用户答案和正确答案
    const isCorrect = () => {
        if (!userAnswer) return false;
        
        // 如果是选择题，比较索引
        if (item.cate === 1) {
            const correctIndex = letterToIndex(correctAnswer);
            return userAnswer === String(correctIndex);
        }
        
        // 其他题型直接比较
        return userAnswer === correctAnswer;
    };
    
    // 如果用户没有作答
    if (!userAnswer) {
        return 'status-warning';
    }
    
    // 根据是否正确返回对应的类
    return isCorrect() ? 'status-correct' : 'status-wrong';
};

onMounted(() => {
    // console.log(queryData,"达一亿与喜爱")
    getDetails()
    
    // 添加窗口大小变化监听器
    window.addEventListener('resize', resizeCharts)
})

onUnmounted(() => {
    // 清除窗口大小变化监听器
    window.removeEventListener('resize', resizeCharts)
    
    // 销毁图表实例
    difficultyChart?.dispose()
    answerChart?.dispose()
})

const getDetails = () => {
    reportState.loading = true

    let params={
        studentPaperId:queryData.studentPaperId
    }
    assignmentsReportApi(params).then((res: any) => {

        if (res.code == 200) {
            secondsToHMS(Number(res.data.answerTime)/1000)
            reportState.title = res.data.title
            reportState.correctRate = res.data.correctRate
            // reportState.answerTime = res.data.answerTime
            reportState.topicScore = res.data.topicScore
            reportState.correct = Number(res.data.correct)
            reportState.quesCount = Number(res.data.quesCount)
            reportState.correctCount = Number(res.data.correctCount) // 更新正确题数
            pointData.value = res.data.quesItemList || []
            
            // 过滤掉题量为0的难度行
            const filteredDegree = (res.data.quesDegree || []).filter(item => item.quesCount > 0)
            degreeData.value = filteredDegree
            
          //筛选推荐知识点，不取1
            let list:any = res.data.reportJson.pointJson || [],list2:any=[]
            if (list.length) {
              for (let i of list) {
                if (i.status != 1){
                  list2.push(i)
                }
              }
            }
          pointVos.value = list2
          
          // 初始化图表
          setTimeout(() => {
              initDifficultyChart()
              initAnswerChart()
          }, 300)
        }
        reportState.loading = false

    }).catch((error) => {
        reportState.loading = false
    })
}
const share = () => {
    //生成小程序-会员权益
    let param = {
      scene: queryData.trainingId + '_' + learnNow.value.idNumber,
      type: 3
    }
    createAppletCodeApi(param).then((res : any) => {
      // blob图片
      reportState.qrcode = window.URL.createObjectURL(res.data)
      reportState.dialog = true
    })
}
const goDetail = () => {
    // pageSource 1是真题试卷，2是文科AI精准学去测评, 3是理科知识图谱单元测试
    let url = ''
    if (queryData.pageSource == '1') {
        url = '/true_paper/true_paper_page/training_report_wenT'
    } else if (queryData.pageSource == '2') {
        url = '/ai_percision/go_evaluate/training_report_wenE'
    } else if (queryData.pageSource == '3') {
        url = '/ai_percision/knowledge_graph_detail/training_report'
    }
    router.push({
        path: url,
        query: {
            data: dataEncrypt({
                trainingId: queryData.trainingId,
                pageSource: queryData.pageSource
            })
        }
    })
}
function secondsToHMS(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    // 补零操作，确保两位数显示
    const pad = (num) => num.toString().padStart(2, '0');
    reportState.answerTime = pad(hours) + ': ' + pad(minutes) + ':' + pad(secs)

}
  // 判断会员状态
  const getIsMember = () => {
    const isVip = useUserStore().memberInfo
    if (isVip) {
      return true
    } else {
      reportState.showVip = true
      return false
    }
  }
// 点击知识点微课
const goWklist = (e : any) => {
    if (getIsMember()) {
      const subject = Number(subjectObj.value.id)
      const { id } = e.currentTarget.dataset
      if (wenke.includes(subject) || subject >= 30) {
        //文科、高中原知识点
        router.push({ name: "NoteWkvideo3", query: { pointId: id, type: 'note', subject } })
      } else {
        //初中小学优学派
        router.push({ name: "NoteWkvideo4", query: { pointId: id, type: 'note', subject } })
      }
    }
  }

  const goBack = () => {
    // 检查多种方式来判断上一个页面是否是 assignment
    const currentUrl = window.location.href;
    const referrer = document.referrer;
    const historyState = window.history.state;
    
    // 判断是否来自 assignment 页面
    const isFromAssignment = 
      referrer.includes('/assignment') || 
      referrer.includes('Assignment') ||
      historyState?.back?.includes('/assignment') ||
      historyState?.back?.includes('Assignment') ||
      // 也可以通过路由历史记录判断
      (typeof router.options.history.state?.back === 'string' && router.options.history.state.back.includes('assignment'));
    
    console.log('返回判断:', {
      referrer,
      historyState,
      isFromAssignment
    });
    
    if (isFromAssignment) {
      // 如果来自 assignment 页面，直接跳转到督学任务页面
      console.log('从assignment页面来，跳转到督学任务页面');
      router.push({ name: 'schoolInspector' });
    } else {
      // 其他情况使用默认的返回行为
      console.log('其他页面来，使用go(-1)');
      router.go(-1);
    }
  };
  
</script>
<style lang="scss" scoped>
.container {
    background: #ffffff;
    width: 100%;
    height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
    .top-box {
        padding-top: 1.25rem;
        background: #ffffff;
        .title {
            display: flex;
            justify-content: center;

            .title-box {
                display: flex;
                align-items: center;
                .test-icon {
                    width: 1.125rem;
                    height: 1.125rem;
                }
                .test-title {
                    color: #2a2b2a;
                    font-size: 1rem;
                    font-weight: 700;
                    margin-left: .5rem;
                    margin-right: .625rem;
                }
            }
        }
         .title-handle {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 1.25rem;
            padding: .625rem 1.25rem 1.25rem;
            border-bottom: .0625rem solid #eaeaea;
            .title-data {
                display: flex;
                margin-top: .625rem;
                margin-bottom: .625rem;
                &-item {
                    display: flex;
                    align-items: center;
                    border-radius: .875rem;
                    padding: .375rem .6875rem;
                    background: #f5f5f5;
                    font-size: .875rem;
                    font-weight: 400;
                    margin-right: .625rem;
                    img {
                        width: 1rem;
                        height: 1rem;
                        margin-right: .3125rem;
                    }
                    span {
                        font-weight: 700;
                    }
                }
            }
            .btn {
                width: 7.625rem;
                height: 2.375rem;
                line-height: 2.375rem;
                text-align: center;
                font-size: 1rem;
                border-radius: 1.1875rem;
                background: #00c9a3;
                cursor: pointer;
                color: #ffffff;
            }
            .green-underline {
                text-decoration: underline;
                color: #009c7f;
                font-size: .875rem;
                font-weight: 400;
                cursor: pointer;
            }
        }
    }
    .middle-box {
        height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 9.375rem);
        overflow-y: auto;
        .middle-table {
            .table-title {
                color: #009c7f;
                font-size: 1rem;
                font-weight: 700;
                margin-top: 1.875rem;
                display: flex;
                align-items: center;
                span {
                    display: inline-block;
                    width: .875rem;
                    height: 1rem;
                    border-radius: 0 .375rem .375rem 0;
                    background: #009c7f;
                    margin-right: .375rem;
                }
            }
            .table {
                margin: .625rem 1.25rem;
                width: 78.75rem;
            }
            .recommond {
                margin: 2.4375rem 1.25rem .625rem 1.25rem;
                width: 78.75rem;
                height: 5.25rem;
                background: linear-gradient(179.2deg, #f3fff0 0%, #d8fedd 56.99999999999999%, #f0faeb 100%);
                img {
                    width: 8.4375rem;
                    height: 1.5rem;
                    margin-left: .8125rem;
                    margin-top: .625rem;
                }
                p {
                    margin-left: 3.9375rem;
                    margin-top: .625rem;
                    color: #2a2b2a;
                    font-size: .875rem;
                    font-weight: 400;
                }
            }
            .knowledge-video {
                margin: 0 1.25rem;
                .knowledge-item {
                    margin-top: 1.25rem;
                    cursor: pointer;
                    display: inline-flex;
                    align-items: center;
                    padding: .375rem .75rem;
                    color: #009c7f;
                    font-weight: 400;
                    font-size: .875rem;
                    border-radius: .9688rem;
                    background: #f5f5f5;
                    margin-right: .625rem;
                    img {
                        width: 1rem;
                        height: 1rem;
                        margin-right: .375rem;
                    }
                }
            }
            .question-result-table {
                margin: 1.875rem 1.25rem;
                width: 78.75rem;
                .result-table {
                    border: .0625rem solid #eaeaea;
                    border-radius: .625rem;
                    overflow: hidden;
                    .table-header {
                        background-color: #f5f5f5;
                        .header-row {
                            display: flex;
                            .header-cell {
                                flex: 1;
                                padding: .625rem .875rem;
                                font-size: .875rem;
                                font-weight: 700;
                                color: #303133;
                                border-bottom: .0625rem solid #eaeaea;
                                text-align: center;
                                &.first-cell {
                                    width: 5rem;
                                    flex: none;
                                }
                            }
                        }
                    }
                    .table-body {
                        .body-row {
                            display: flex;
                            .body-cell {
                                flex: 1;
                                padding: .625rem .875rem;
                                font-size: .875rem;
                                color: #606266;
                                border-bottom: .0625rem solid #eaeaea;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                &.first-cell {
                                    width: 5rem;
                                    flex: none;
                                }
                                
                                .status-icon {
                                    width: 1.5rem;
                                    height: 1.5rem;
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    color: #ffffff;
                                    
                                    &.status-correct {
                                        background-color: #67C23A;
                                    }
                                    
                                    &.status-wrong {
                                        background-color: #F56C6C;
                                    }
                                    
                                    &.status-warning {
                                        background-color: #E6A23C;
                                    }
                                    
                                    .el-icon {
                                        font-size: 0.875rem;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
.black-text1 {
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: 400;
    text-align: center;
}
.dialog-footer {
    display: flex;
    justify-content: center;
    margin-top: 1.875rem;
}
.pt_qr {
    width: 12rem;
    height: 12rem;
    border-radius: 1.875rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .pt_qr img {
    width: 10.25rem;
    height: 10.25rem;
  }
.blue-btn {
    width: 7.625rem;
    height: 2.375rem;
    line-height: 2.375rem;
    text-align: center;
    border-radius: 1.1875rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
    margin-right: .625rem;
    font-weight: 400;
}
.icon-btn {
    border-radius: .25rem;
    background: #00c9a3;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
        margin-right: .3125rem;
    }
}

.center-box{
  display: flex;
  align-items: center;
  justify-content: center;
}
.table-status{
  width: 4.625rem;
  font-size: .875rem;
  height: 1.4375rem;
  line-height: 1.4375rem;
  color: #ffffff !important;
  border-radius: 1.125rem;
  margin: .5rem 0 0;
}
.green-box{
  background: linear-gradient(166.7deg, #08d8b8 0%, #00b392 100%);
}
.yellow-box{
  background: linear-gradient(150.8deg, #f6d22b 0%, #f29500 100%);
}
.red-box{
  background: linear-gradient(150.8deg, #f07f4c 0%, #c95656 100%);
}
.page-header {
  margin-bottom: 18px;
}

/* 图表容器样式 */
.charts-container {
  display: flex;
  justify-content: space-around;
  margin: 20px 0;
  padding: 0 20px;
  
  .chart-box {
    width: 45%;
    max-width: 400px;
    height: 300px;
    position: relative;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
    
    .chart-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      text-align: center;
      margin-bottom: 10px;
    }
    
    .chart {
      height: 220px;
      width: 100%;
    }
    
    .chart-legends {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      margin-top: 10px;
      
      .legend-item {
        display: flex;
        align-items: center;
        margin: 0 10px 5px 0;
        
        .legend-color {
          width: 10px;
          height: 10px;
          border-radius: 2px;
          margin-right: 5px;
        }
        
        .legend-text {
          font-size: 12px;
          color: #606266;
          margin-right: 5px;
        }
        
        .legend-count {
          font-size: 12px;
          color: #909399;
        }
      }
    }
    
    .chart-center-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      
      .correct-count {
        font-size: 36px;
        font-weight: bold;
        color: #00c9a3;
        line-height: 1;
      }
      
      .total-count {
        font-size: 16px;
        color: #909399;
      }
      
      .text-label {
        font-size: 14px;
        color: #606266;
        margin-top: 5px;
      }
    }
  }
}

.breadcrumbs {
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
}

.breadcrumbs .back-link {
  color: #00bfa5;
  font-size: 14px;
  text-decoration: none;
}
.breadcrumbs .back-link:hover {
  color: #00bfa5;
}

.breadcrumb-separator {
  color: #c0c4cc;
  margin: 0 5px;
}

.breadcrumb-item {
  color: #606266;
}
.breadcrumb-item.active {
  color: #303133;
  font-weight: 500;
}

.main-content {
  background-color: #f5f5f5;
  border-radius: 6px;
  padding: 20px 24px;
}
</style>
<style lang="scss">
.dialog-share {
    width: 23.125rem!important;
    border-radius: 1.25rem;
    box-sizing: border-box;
    .el-dialog__header {
        border: none!important;
    }
}
</style>
