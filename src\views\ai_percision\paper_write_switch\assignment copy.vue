<template>
    <header class="page-header">
      <div class="breadcrumbs">
        <a href="#" class="back-link" @click.prevent="goBack">&lt; 返回</a>
        <span class="breadcrumb-separator">&nbsp;/&nbsp;</span>
        <span class="breadcrumb-item">{{ writeState.showSubjective ? '批改' : '作答' }}</span>
      </div>
    </header>
    <div class="container" v-loading="writeState.loading">

      <div class="left">
        <div class="top-box">
            <div class="title-handle">
                <div class="title-box">
                    <img class="test-icon" src="@/assets/img/percision/title.png" />
                    <div class="test-title">{{ queryData.title }} </div>
                </div>
            </div>
        </div>
        <div class="test-content">
            <div v-for="(item, index) in allTest" :key="item.index" class="test-content-ques" :class="setClass(item, index)" v-show="getShowState(item)">
                <div class="squre"></div>
                <div class="test-tittle">
                    <div v-html="resetSty(item, index + 1)" />
                </div>
                <div class="test-body" v-html="resetOptions(item)" />
                <div>
                    <!-- 选择题 -->
                    <div v-if="item.cate == 1 || item.cate == 3" class="paper-content-ques">
                        <!-- 单选题 -->
                        <div v-if="item.cate == 1">
                            <!-- 批改模式下显示选择情况 -->
                            <div v-if="writeState.showSubjective">
                                <div class="selected-options">
                                    已选择: {{ item.userJson && item.userJson.length > 0 ? String.fromCharCode(65 + item.userJson[0]) : '无' }}
                                </div>
                                <!-- 显示答案与解析 -->
                                <div>
                                    <div class="show-analyse">
                                        <el-switch @change="togAnswer(item, item.showAnalyse)" size="small" v-model="item.showAnalyse" /> <span>显示答案与解析</span>
                                    </div>
                                    <div v-show="item.showAnalyse" class="analyse">
                                        <div class="flex-sty">
                                            <span>【答案】</span>&nbsp;&nbsp;
                                            <div v-html="item.displayAnswer" />
                                        </div>
                                        <div class="flex-sty">
                                            <span>【分析】</span>&nbsp;&nbsp;
                                            <div v-html="item.analyse" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 作答模式下显示选择框 -->
                            <el-checkbox-group v-else v-model="item.userJson" class="checkbox-style-checkbox">
                                <el-checkbox
                                    v-for="(it,ind) in item.options"
                                    :key="ind"
                                    @change="checkChange(ind,index)"
                                    :label="ind"
                                    size="large" border
                                >
                                {{ String.fromCharCode(65 + ind) }}
                                </el-checkbox>
                            </el-checkbox-group>
                        </div>
                        <!-- 多选题 -->
                        <div v-else>
                            <!-- 批改模式下显示选择情况 -->
                            <div v-if="writeState.showSubjective">
                                <div class="selected-options">
                                    已选择: {{ item.userJson && item.userJson.length > 0 ? item.userJson.map(i => String.fromCharCode(65 + i)).join(', ') : '无' }}
                                </div>
                                <!-- 显示答案与解析 -->
                                <div>
                                    <div class="show-analyse">
                                        <el-switch @change="togAnswer(item, item.showAnalyse)" size="small" v-model="item.showAnalyse" /> <span>显示答案与解析</span>
                                    </div>
                                    <div v-show="item.showAnalyse" class="analyse">
                                        <div class="flex-sty">
                                            <span>【答案】</span>&nbsp;&nbsp;
                                            <div v-html="item.displayAnswer" />
                                        </div>
                                        <div class="flex-sty">
                                            <span>【分析】</span>&nbsp;&nbsp;
                                            <div v-html="item.analyse" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 作答模式下显示选择框 -->
                             
                            <el-checkbox-group v-else v-model="item.userJson" class="checkbox-style-checkbox">
                                <el-checkbox
                                    v-for="(it,ind) in item.options"
                                    :key="ind"
                                    :label="ind"
                                    size="large" border
                                >
                                {{ String.fromCharCode(65 + ind) }}
                                </el-checkbox>
                            </el-checkbox-group>
                        </div>
                    </div>
                    <!-- 主观题 -->
                    <div v-else class="paper-content-ques2">
                        <!-- 批改模式 -->
                        <div v-if="writeState.showSubjective">
                            <!-- 已上传图片的情况v-if="item.userJson && item.userJson.length > 0" -->
                            <div >
                                <div class="show-analyse">
                                    <el-switch @change="togAnswer(item,item.showAnalyse)" size="small" v-model="item.showAnalyse" /> <span>显示答案与解析</span>
                                </div>
                                <div v-show="item.showAnalyse" class="analyse">
                                    <div class="flex-sty">
                                        <span>【知识点】</span>&nbsp;&nbsp;
                                        <div v-if="item.pointVos && item.pointVos.length > 0" v-html="item.pointVos[0].name" />
                                        <div v-else>--</div>
                                    </div>
                                    <div class="flex-sty">
                                        <span>【答案】</span>&nbsp;&nbsp;
                                        <div v-html="item.displayAnswer" />
                                    </div>
                                    <div class="flex-sty">
                                        <span>【分析】</span>&nbsp;&nbsp;
                                        <div v-html="item.analyse" />
                                    </div>
                                    <div class="flex-sty">
                                        <span>【解答】</span>&nbsp;&nbsp;
                                        <div v-html="item.method" />
                                    </div>
                                    <div class="flex-sty">
                                        <span>【点评】</span>&nbsp;&nbsp;
                                        <div v-html="item.discuss" />
                                    </div>
                                </div>
                                <div class="answer-img-box">
                                    <el-image
                                        class="answer-img"
                                        v-for="(it, ind) in item.userJson"
                                        style="width: 10.8125rem; height: 10.8125rem;border-radius: .25rem;"
                                        :src="it.url || it"
                                        :zoom-rate="1.2"
                                        :max-scale="7"
                                        :min-scale="0.2"
                                        :preview-src-list="item.userJson.map(img => img.url || img)"
                                        show-progress
                                        :initial-index="ind"
                                        fit="cover"
                                    />
                                </div>
                                <div class="answers">
                                    <div class="answer-box" @click="correcthandle(index, 1)" :class="item.userMark == 1?'green-box':''">
                                        <div></div>正确
                                    </div>
                                    <div class="answer-box" @click="correcthandle(index, 2)" :class="item.userMark == 2?'yellow-box':''">
                                        <div></div>半对
                                    </div>
                                    <div class="answer-box" @click="correcthandle(index, 0)" :class="item.userMark == 0?'red-box':''">
                                        <div></div>错误
                                    </div>
                                </div>
                            </div>
                            <!-- 未上传图片的情况，提供上传功能 -->
                            <!-- <div v-else class="padding2030">
                                <div class="tip-text">请上传答案图片后进行批改</div>
                                <uploadAnswerImg :imgList="item.userJson" :index="index" @getImgList="handleImgList" />
                            </div> -->
                        </div>
                        <!-- 作答模式 -->
                        <div v-else class="padding2030">
                            <uploadAnswerImg :imgList="item.userJson" :index="index" @getImgList="handleImgList" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
      </div>
      <div class="right">
        <div class="time-box">
            <div class="time-text">用时:</div>
            <div class="time-number"> {{ timeState.hours < 10 ? "0" + timeState.hours : timeState.hours }} </div> :
            <div class="time-number"> {{ timeState.minutes < 10 ? "0" + timeState.minutes : timeState.minutes }} </div> :
            <div class="time-number"> {{ timeState.seconds < 10 ? "0" + timeState.seconds : timeState.seconds }} </div>
        </div>
        <div class="test-number-box">
            <div class="test-number-item" v-for="(item, index) in allTest" 
                 :class="setClass1(item, index)" 
                 @click="switchQuestion(index)"> {{ index + 1 }} </div>
            <!-- 参考index.vue，根据isAllCorrect判断是否显示提交按钮 -->
            <div v-show="!writeState.showSubjective || writeState.isAllCorrect" class="icon-btn size285" @click="submitAllAnswers" v-loading="writeState.btnloading">
                <img src="@/assets/img/percision/submit.png" alt="">
                提交批改
            </div>
            <!-- 批改模式下且未全部批改完成时显示提示按钮 -->
            <div v-show="writeState.showSubjective && !writeState.isAllCorrect" class="icon-btn size285 disabled">
                <img src="@/assets/img/percision/submit.png" alt="">
                完成批改后可提交
            </div>
            <!-- <div v-if="reviewerType == '0'" class="icon-btn size285 disabled">
                <img src="@/assets/img/percision/submit.png" alt="">
                提交作业
            </div> -->

        </div>
      </div>
      <div class="five-step-box" v-if="writeState.showStep">
        <fiveStep :sourceId="queryData.sourceId" :type="1" :update="false" @sendStep="sendStep"></fiveStep>
      </div>
    </div>
    <el-dialog 
        class="dialog-correct" 
        v-model="writeState.showDialog" 
        title="提交" 
        align-center 
        center
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false">
        <p class="black-text" style="text-align: center;">{{ writeState.subjectiveNum }}道主观题需要对照答案批改</p>
        <p class="grey-text" style="text-align: center;">（同学们可以邀请家长一起完成哦）</p>
        <template #footer>
        <div class="dialog-footer">
            <div class="blue-btn" @click="toCorrect">去批改</div>
        </div>
        </template>
    </el-dialog>
</template>
  
<script lang="ts" setup>
import { watch, onMounted, reactive, ref, onUnmounted, computed } from 'vue'
import uploadAnswerImg from '@/views/components/uploadAnswerImg/index.vue'
import coinAlert from "@/views/components/coinAlert/index.vue"
import { dataDecrypt, dataEncrypt, mergeObject } from "@/utils/secret"
import { useRouter, useRoute } from 'vue-router'
import fiveStep from "@/views/components/fiveStep/index.vue"
import { quesGetApi } from "@/api/video"
import { Action, ElMessage, ElMessageBox } from 'element-plus'
import { paperQuesApi, submitAssignment, correctAssignmentApi } from '@/api/online'
import { el } from 'element-plus/es/locale'

const route = useRoute()
const router = useRouter()
const isclick = ref()
const timeState = reactive({
    hours: 0,
    minutes: 0,
    seconds: 0
})
const writeState = reactive({
    current: 0 as number | null,
    step: 1,
    btnloading: false,
    loading: false,
    showStep: false,
    disabled: true,
    trainingId: "",
    //积分
    jfShow: false,
    jfHide: true,
    jfNum: '0',
    jfSource: '0',
    itemTimer: 0, // 单题计时器
    lastTimestamp: 0, // 上次计时时间戳
    showDialog: false, // 添加显示对话框状态
    subjectiveNum: 0, // 添加主观题数量
    showSubjective: false, // 是否只显示主观题
    unWriteNum: [] as number[], // 未作答的题号
    isAllCorrect: false // 是否所有主观题都已批改
})
interface Ques {
    cate: number;
    cateName: string;
    content: string;
    displayAnswer: string;
    analyse: string;
    method: string;
    discuss: string;
    options: any[];
    pointVos: any[];
    userJson: any[];
    answers: any[];
}

class AData {
    quesId: string = "";
    cate: number = 0;
    cateName: string = "";
    trainTime: string = "";
    userAnswer: string[] = [];
    userMark: number | null = null;
    userMarks: number | null = null;
    showAnalyse: boolean = false;
    content: string = "";
    ques: Ques = { // 添加 ques 属性
        cate: 0,
        cateName: "",
        content: "",
        analyse: "",
        discuss: "",
        method: "",
        displayAnswer: "",
        options: [],
        pointVos: [],
        userJson: [],
        answers: []
    };
}
let detailData
let timer :  NodeJS.Timeout | null = null
const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
const allTest = ref([] as any[])

const reviewerType = ref()
// 检查所有题目是否都已作答和批改
const checkAllAnswersComplete = () => {
    const unansweredItems = allTest.value.filter(item => {
        // 选择题只需要有选择
        if (item.cate == 1 || item.cate == 3) {
            return !item.userJson || item.userJson.length === 0;
        } 
        // 非选择题需要上传图片并完成批改
        else {
            return !item.userJson || item.userJson.length === 0 || item.userMark === undefined || item.userMark === null;
        }
    });
    
    return unansweredItems.length === 0;
};

// 计算属性检查所有题目是否已作答
const isAllAnswered = computed(() => {
    return checkAllAnswersComplete();
});

// 另外添加一个方法专门检查是否所有主观题都已批改
const checkAllSubjectivesGraded = () => {
    let result = true;
    
    allTest.value.forEach(item => {
        if (item.cate != 1 && item.cate != 3) {
            // 只检查有上传图片的主观题
            if (item.userMark == null) {
                result = false;
            }
        }
    });

    return result;
}

watch(() => timeState.seconds, () => {
    if(timeState.seconds == 60) {
        timeState.minutes ++
        timeState.seconds = 0
    }
    if(timeState.minutes == 60) {
        timeState.hours ++
        timeState.minutes = 0
    }
})

// 自定义返回方法
const customGoBack = () => {
    router.go(-1)
}

onMounted(() => {
    // 初始化时间戳
    writeState.lastTimestamp = Date.now()
    reviewerType.value = queryData.reviewerType
    console.log(reviewerType.value,"reviewerTypereviewerTypereviewerTypereviewerType")
    getDetails()
    
    // 启动计时器
    timer = setInterval(() => {
        timeState.seconds++
        writeState.itemTimer++ // 更新单题计时
    }, 1000)
    
    window.customGoBack = customGoBack
})

onUnmounted(() => {
    if (timer !== null) { // 添加类型安全检查
        clearInterval(timer)
        timer = null // 确保timer被清空
    }
    // 重置计时器状态
    writeState.itemTimer = 0
    
    // 清除自定义返回方法
    if (window.customGoBack) {
        delete window.customGoBack
    }
})
// 隐藏积分
const jfHide = () => {
    writeState.jfShow = false
}
// 获取学习步骤
const sendStep = ( data: number) => {
    writeState.step = data
}

// 将时分秒转换为毫秒时间戳
const convertTimeToMilliseconds = () => {
    const totalSeconds = timeState.hours * 3600 + timeState.minutes * 60 + timeState.seconds
    return totalSeconds * 1000 // 转换为毫秒
}

const getDetails = () => {
    writeState.loading = true
    paperQuesApi({ studentPaperId: queryData.studentPaperId}).then((res1: any) => {
        if (res1.code == 200) {
            detailData = res1.data
            timeState.seconds = Number(0)  / 1000
            res1.data.forEach((item) => {
                // 确保每个题目对象有必要的属性
                item.showAnalyse = false
                item.userJson = []
                item.loading = false
                item.analyse = ''
                item.method = ''
                item.discuss = ''
                item.displayAnswer = ''
                item.pointVos = []
            })
            allTest.value = res1.data
            
            // 设置第一题为当前题目
            writeState.current = 0
            writeState.itemTimer = 0
        }
        writeState.loading = false

    }).catch((error) => {
        writeState.loading = false
    })
}

const setClass = (item: any, index: number) => {
    let classState = ""
    if (writeState.current == index) {
        classState = "black-text"
    }
    return classState
}

// 修改 setClass1 函数，根据题目完成状态来设置题号样式
const setClass1 = (item: any, index: number) => {
    let classState = ""
    
    // 当前选中的题目显示为蓝色
    // if (writeState.current == index) {
    //     classState = "blue"
    // } 
    // // 已完成的题目显示为绿色
    // else
    if (isQuestionCompleted(item)) {
        classState = "green"
    }
    
    return classState
}

// 判断题目是否已完成（选择题已选择或非选择题已上传图片并批改）
const isQuestionCompleted = (item: any) => {
    if (item.cate == 1 || item.cate == 3) {
        // 选择题：有选择即为完成
        return item.userJson && item.userJson.length > 0;
    } else {
        // 非选择题：需要上传图片并进行批改
        return item.userMark !== undefined && item.userMark !== null;
    }
}

// 添加点击题号切换题目的方法
const switchQuestion = (index: number) => {
    // 允许切换到任何题目
    writeState.itemTimer = 0
    writeState.current = index
}

// 监听当前题目变化
watch(() => writeState.current, (newVal, oldVal) => {
    if (newVal !== oldVal && newVal !== null) {
        // 切换题目时重置单题计时器
        writeState.itemTimer = 0
    }
})

// 更新选择题选择处理函数
const checkChange = (val: any, index: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在且 userJson 已初始化
    const currentItem = allTest.value[index];
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson
    currentItem.userJson = [val];
    
    // 更新提交按钮状态
    updateAllAnsweredState();
    
    // 选择题选择完后自动切换到下一题
    if (index < allTest.value.length - 1) {
        setTimeout(() => {
            writeState.current = index + 1;
        }, 300); // 短暂延迟，让用户感知到选择已完成
    }
}

// 添加处理批改选项点击的函数
const correcthandle = (index: number, userMark: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在
    const currentItem = allTest.value[index];
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新题目的批改状态
    currentItem.userMark = userMark;
    
    // 检查是否所有主观题都已批改完成，参考index.vue实现
    writeState.isAllCorrect = checkAllSubjectivesGraded();
    
    // if (writeState.isAllCorrect) {
    //     ElMessage({
    //         message: '所有主观题已批改完成，可以提交了',
    //         type: 'success',
    //         duration: 2000
    //     });
    // }
    
    // 自动切换到下一题
    if (index < allTest.value.length - 1) {
        // 找到下一个需要批改的主观题
        const nextIndex = findNextSubjectiveQuestion(index);
        if (nextIndex !== -1) {
            setTimeout(() => {
                writeState.current = nextIndex;
            }, 300);
        }
    }
}

// 查找下一个需要批改的主观题
const findNextSubjectiveQuestion = (currentIndex: number) => {
    for (let i = currentIndex + 1; i < allTest.value.length; i++) {
        const item = allTest.value[i];
        // 找到未批改的主观题
        if (item.cate != 1 && item.cate != 3 && 
            item.userJson && item.userJson.length > 0 && 
            (item.userMark === undefined || item.userMark === null)) {
            return i;
        }
    }
    return -1;
}

// 更新提交按钮状态
const updateAllAnsweredState = () => {
    // isAllAnswered 是计算属性，会自动更新
};

// 更新图片上传处理函数
const handleImgList = (index: number, imgList: any) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在
    const currentItem = allTest.value[index];
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson 和重置批改状态（如果有更新图片）
    if (currentItem.userJson && currentItem.userJson.length !== imgList.length) {
        // 图片发生变化，重置批改状态
        currentItem.userMark = null;
    }
    
    currentItem.userJson = imgList;
    
    // 更新提交按钮状态
    updateAllAnsweredState();
    
    // 自动检查是否可以切换到下一题
    if (index < allTest.value.length - 1 && imgList.length > 0) {
        // 图片上传后可以手动点击批改选项，所以不自动切换题目
        // 这里仅更新题号状态，不自动切换
    }
}

const goBack = () => {
  router.go(-1);
};

//过滤掉填空题等div标签的contenteditable属性
const filterContent = (contect: string) => {
  let highlightedResult = ""
  highlightedResult = contect.replaceAll('contenteditable="true"', " ")
  highlightedResult = highlightedResult.replaceAll("contenteditable='true'", " ")
  return highlightedResult
}

//过滤修改题干标签内容
const resetSty = function (testItem: any, sort?: number) {
  const tittle = "（" + testItem.cateName + "）" +sort + "." + '&nbsp;&nbsp;' + filterContent(testItem.content)
  return tittle
}

// 检查是否为全选择题作业
const isAllChoiceQuestionsAssignment = () => {
    return allTest.value.every(item => item.cate == 1 || item.cate == 3);
}

// 检查选择题是否都已选择
const areAllChoiceQuestionsAnswered = () => {
    const choiceQuestions = allTest.value.filter(item => item.cate == 1 || item.cate == 3);
    return choiceQuestions.every(item => item.userJson && item.userJson.length > 0);
}

// 学生批改
const subCorrectAssignment = () => {
    writeState.btnloading = true;
    
    // 题目总数
    const totalQuestions = allTest.value.length;
    
    // 每题的满分值（总分100平均分配，精确到小数点后两位）
    const scorePerQuestion = totalQuestions > 0 ? parseFloat((100 / totalQuestions).toFixed(2)) : 0;
    
    // 准备所有题目的批改数据
    const quesItems = allTest.value.map(item => {
        // 优化选择题答题逻辑：如果已选择选择题全部提交，只针对未选择选择题才判错
        let userMark, earnedScore, correctRate;
        
        if (item.cate == 1 || item.cate == 3) {
            // 选择题逻辑
            if (item.userJson && item.userJson.length > 0) {
                // 已选择的选择题：默认正确（可以后续由系统批改）
                userMark = 1; // 暂时标记为正确，实际正确性由系统判断
                earnedScore = scorePerQuestion;
                correctRate = 100;
            } else {
                // 未选择的选择题：判为错误
                userMark = 0;
                earnedScore = 0;
                correctRate = 0;
            }
        } else {
            // 主观题逻辑：使用原有的批改结果
            userMark = item.userMark ?? 0;
            earnedScore = userMark === 1 ? scorePerQuestion : userMark === 2 ? parseFloat((scorePerQuestion / 2).toFixed(2)) : 0;
            correctRate = userMark === 1 ? 100 : userMark === 2 ? 50 : 0;
        }
        
        // 计算单题得分（精确到小数点后两位）
        const topicScore = parseFloat(scorePerQuestion.toFixed(2));
        
        // 构建correctItem数组
        const correctItem = [{
            topicScore: parseFloat(earnedScore.toFixed(2)), // 实际得分精确到小数点后两位
            userMark,
            correctRate
        }];
        
        console.log(`题目 ${item.quesId} 处理结果:`, {
            题目类型: item.cate == 1 || item.cate == 3 ? '选择题' : '主观题',
            是否已答: item.userJson && item.userJson.length > 0 ? '是' : '否',
            批改结果: userMark,
            得分: parseFloat(earnedScore.toFixed(2)), // 单题得分精确到小数点后两位
            正确率: correctRate
        });
        
        return {
            quesId: item.quesId,
            topicScore: parseFloat(earnedScore.toFixed(2)), // 单题得分精确到小数点后两位
            correctItem,
            userMark,
            correctRate
        };
    });
    
    // 计算总得分和整体正确率
    let totalEarnedScore = 0;
    let totalCorrect = 0;
    
    quesItems.forEach((item, index) => {
        const originalItem = allTest.value[index];
        
        if (item.userMark === 1) {
            totalCorrect += 1;
            totalEarnedScore += item.topicScore; // 使用实际计算的得分
        } else if (item.userMark === 2) {
            totalCorrect += 0.5;
            totalEarnedScore += item.topicScore; // 使用实际计算的得分
        }
        // userMark === 0 的情况不加分
    });
    
    // 总得分四舍五入为整数
    const finalTotalScore = Math.round(totalEarnedScore);
    const overallCorrectRate = totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100) : 0;
    
    console.log('最终统计结果:', {
        总题数: totalQuestions,
        每题满分: scorePerQuestion,
        累计得分: parseFloat(totalEarnedScore.toFixed(2)), // 累计得分（小数）
        总得分: finalTotalScore, // 总得分（整数）
        总正确数: totalCorrect,
        整体正确率: overallCorrectRate
    });
    
    // 构建完整请求数据
    const requestData = {
        schoolAssignmentStudentId: queryData.schoolId,
        topicScore: finalTotalScore, // 总得分（四舍五入为整数）
        correctRate: overallCorrectRate, // 正确率
        status: 1, // 已完成状态
        quesItems,
        reviewerType: reviewerType.value || 0
    };
    
    
    correctAssignmentApi(requestData).then((res: any) => {
        writeState.btnloading = false;
        
        if (res.code === 200) {
            ElMessage({
                message: '批改提交成功',
                type: 'success',
                duration: 2000
            });
            
            // 根据reviewerType决定后续操作
            if (reviewerType.value == 1) {
                router.push({
                    name: 'Analysis',
                    query: {
                        data: dataEncrypt({
                            studentPaperId: queryData.studentPaperId,
                            pageSource: '1',
                            title:queryData.title
                        }),
                    }
                });
            } else {
                setTimeout(() => {
                    router.go(-1);
                }, 1500);
            }
        } else {
            ElMessage({
                message: res.msg || '批改提交失败',
                type: 'error',
                duration: 2000
            });
        }
    }).catch(error => {
        writeState.btnloading = false;
        console.error('批改提交错误:', error);
        ElMessage({
            message: '批改提交失败，请稍后重试',
            type: 'error',
            duration: 2000
        });
    });
}
//过滤修改选项内容
const resetOptions = function (testItem: any) {
    let optionHtml = ""
    if (!testItem.options) return
    testItem.options.map((item: any, index: number) => {
        optionHtml += `<div class="answer-item"> ${String.fromCharCode(65 + index)}. <div style="display:inline-table;max-width:39.8125rem">${item}</div></div>`
    })
    return optionHtml
}

// 一次性提交所有答案
const submitAllAnswers = () => {
    // 如果在批改模式下
    if (writeState.showSubjective) {
        console.log(1111)
        // 检查是否所有已答主观题都已批改
        if (!writeState.isAllCorrect) {
            ElMessage({
                message: '请完成所有已答主观题的批改后再提交',
                type: 'warning'
            });
            return;
        }
        
        // 可以提交
        ElMessageBox.confirm('确认提交所有答案?', '提交确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            // type: 'warning',
            center: true,
            dangerouslyUseHTMLString: true,
            message: '<div style="text-align: center; font-size: 16px;">确认提交所有答案?</div>'
        }).then(() => {
            submitPaper();
            subCorrectAssignment()

        }).catch(() => {
            // 用户取消提交
        });
        return;
    }

    // 检查未答题目
    writeState.unWriteNum = [];
    writeState.subjectiveNum = 0;
    
    // 统计未答题目和主观题数量
    allTest.value.forEach((item, index) => {
        if (!item.userJson || item.userJson.length === 0) {
            writeState.unWriteNum.push(index + 1);
        }
        
        if (item.cate != 1 && item.cate != 3) {
            writeState.subjectiveNum++;
        }
    });
    
    // 如果有未作答的题目，询问是否继续提交
    if (writeState.unWriteNum.length > 0) {
        ElMessageBox.confirm(
            `第${writeState.unWriteNum.toString().replaceAll(',', '、 ')}题未作答，您确定要提交吗？`, 
            '提交确认', 
            {
                confirmButtonText: '继续答题',
                cancelButtonText: '确定提交',
                distinguishCancelAndClose: true,
                type: 'warning',
                center: true,
                dangerouslyUseHTMLString: true,
                message: `<div style="text-align: center; font-size: 16px;">第${writeState.unWriteNum.toString().replaceAll(',', '、 ')}题未作答，您确定要提交吗？</div>`
            }
        ).then(() => {

            console.log("取消取消")

            // 用户选择继续答题
        }).catch((action: Action) => {
            // 区分取消还是关闭
            if (action === 'cancel') {
                // 用户选择提交

                if (writeState.subjectiveNum > 0) {

                    // 有主观题，显示批改对话框
                    // writeState.showDialog = true;
                    submitPaper();
                    // if(reviewerType.value == 1){
                    //     submitPaper();
                    // }else{
                    //     submitPaper();
                    // }
                } else {
                    // 无主观题，检查是否全是选择题
                    if (isAllChoiceQuestionsAssignment()) {
                        console.log("全选择题作业，有未答题目，直接提交并批改");
                        // 全选择题，直接进入批改流程（未选择的题目会被判为错误）
                        // subCorrectAssignment();
                        submitPaper()
                    } else {
                        console.log(2222)
                        submitPaper();
                    }
                }
                
                if (timer !== null) {
                    clearInterval(timer);
                    timer = null;
                }
            }
        });
    } else {

        // 全部已作答，判断是否有主观题需要批改
        if (writeState.subjectiveNum > 0) {

            // writeState.showDialog = true;
            submitPaper();
        } else {
            // 无主观题，检查是否全是选择题
            if (isAllChoiceQuestionsAssignment()) {
                console.log("全选择题作业，全部已答，直接提交并批改");
                // 全选择题，直接进入批改流程
                subCorrectAssignment();
            } else {
                console.log("来到了这个了")
                submitPaper();
            }
        }
        
        if (timer !== null) {
            clearInterval(timer);
            timer = null;
        }
    }
}

// 去批改主观题
const toCorrect = () => {
    // 切换到批改模式
    writeState.showDialog = false;
    writeState.showSubjective = true;
    
    // 参考index.vue的实现，处理图片格式
    allTest.value.forEach((item) => {
        // console.log(item,"itemitem7232itemitem7232itemitem7232itemitem7232")
        if (item.cate != 1 && item.cate != 3 && item.userJson && item.userJson.length > 0) {
            // 如果是主观题且已上传图片，转换格式以便批改
            const UrlArr = JSON.parse(JSON.stringify(item.userJson)) || [];
            item.userJson = UrlArr.map((imgItem: any) => { 
                return typeof imgItem === 'string' ? imgItem : imgItem.url; 
            });

            item.images = [...item.userJson]; // 保存图片URL备份

        }
    });
    
    // 初始状态下，可能没有主观题全部批改完成，所以设为false
    writeState.isAllCorrect = checkAllSubjectivesGraded();
    
    // 尝试找到第一个需要批改的主观题
    const firstSubjective = findNextSubjectiveQuestion(-1);
    if (firstSubjective !== -1) {
        writeState.current = firstSubjective;
    }
}

// 提交试卷
const submitPaper = () => {
    writeState.btnloading = true;
        // 准备所有题目的答案数据，包括批改结果
    const items = allTest.value.map((item) => {
            // 处理选择题
            if (item.cate == 1 || item.cate == 3) {
                return {
                    cate: item.cate,
                    userJson: item.userJson || [],
                    studentPaperId: queryData.studentPaperId,
                    quesId: item.quesId
                }
            } 
            // 处理非选择题，包含批改结果
            else {
            const imgUrls = item.userJson && item.userJson.length > 0 
                ? item.userJson.map((imgItem: any) => imgItem.url)
                : [];
            // console.log(imgUrls,"imgUrlsimgUrlsimgUrlsimgUrls")
                return {
                    userJson: imgUrls,
                    // userJson: item.userJson || [],
                    quesId: item.quesId,
                    userMark: item.userMark, // 添加批改结果
                    isBase64: false
                }
            }
    });
    
        // 确保使用最新的计时值
    const finalTrainTime = convertTimeToMilliseconds();
        const params = {
            studentPaperId: queryData.studentPaperId,
            times: finalTrainTime,
            status: 2,   //状态：（1：未完成、2：已完成）
            items: items
        }

        // return;
        // 使用submitAssignment API提交所有答案
        submitAssignment(params)
        .then((res: any) => {
            if (timer !== null) {
                clearInterval(timer)
                timer = null
            }
            
            writeState.btnloading = false
            
            if (res.code === 200) {
                console.log(reviewerType.value,"reviewerType.valuereviewerType.value")

                 if (writeState.subjectiveNum > 0) {
                    if(reviewerType.value == 1){
                        writeState.showDialog = true;
                        isclick.value = true
                    }else{

                    ElMessage({
                        message: '提交成功',
                        type: 'success'
                    })
                    setTimeout(() => {
                        router.go(-1)
                    }, 3000);
                    }
                }else{
                    setTimeout(() => {
                    router.push({
                        name: 'Analysis',
                        query: {
                            data: dataEncrypt({
                                studentPaperId: queryData.studentPaperId,
                                pageSource: '1',
                                title:queryData.title
                            }),
                        }
                     });
                    }, 1000);
                 }
            } else {
                ElMessage({
                    message: res.msg,
                    type: 'success'
                })
                ElMessage({
                    message: res.msg || '提交失败',
                    type: 'error'
                })
            }
        })
        .catch((error) => {
            console.error('提交失败:', error)
            writeState.btnloading = false
            ElMessage({
                message: '提交失败，请稍后重试',
                type: 'error'
            })
    })
}


// 添加函数
const getShowState = (item: any) => {
    // 如果处于批改模式
    if (writeState.showSubjective) {
        // 在批改模式下只显示主观题，与index.vue保持一致
        if (item.cate == 1 || item.cate == 3) {
            return false; // 不显示选择题
        } else {
            return true; // 显示主观题
        }
    }
    // 否则显示所有题目
    return true;
}

//显示答案
const togAnswer = async (item:any, isShow:any) => { 
    if(isShow){
        try {
            // 添加加载状态，防止重复点击
            if (item.loading) return;
            item.loading = true;           
            const response = await quesGetApi({id: item.quesId}) as any;
            
            if (response.code === 200 && response.data) {
                // 合并数据
                item.analyse = response.data.analyse || '';
                item.method = response.data.method || '';
                item.discuss = response.data.discuss || '';
                item.displayAnswer = response.data.displayAnswer || '';
                item.pointVos = response.data.pointVos || [];
                
                console.log('题目详细信息已获取:', item);
            } else {
                console.error('获取题目详细信息失败:', response);
                // 如果获取失败，关闭显示开关
                item.showAnalyse = false;
                ElMessage({
                    message: '获取题目详情失败',
                    type: 'error'
                });
            }
        } catch (error) {
            console.error('获取题目详细信息时发生错误:', error);
            // 发生错误时关闭显示开关
            item.showAnalyse = false;
            ElMessage({
                message: '获取题目详情失败',
                type: 'error'
            });
        } finally {
            // 清除加载状态
            item.loading = false;
        }
    }
}
</script>
<style lang="scss" scoped>
.container {
    display: flex;
    position: relative;
    .five-step-box {
        position: absolute;
        right: -8.75rem;
        top: 11.25rem;
    }
    .left {
        width: 60.3125rem;
        .top-box {
            padding: 1.25rem;
            background: #ffffff;
            margin-bottom: 10px;
            .title-handle {
                display: flex;
                justify-content: space-between;
                .title-box {
                    display: flex;
                    align-items: center;
                    .test-icon {
                        width: 1.125rem;
                        height: 1.125rem;
                    }
                    .test-title {
                        color: #2a2b2a;
                        font-size: 1rem;
                        font-weight: 700;
                        margin-left: .5rem;
                        margin-right: .625rem;
                    }
                    .size84 {
                        width: 5.25rem;
                        height: 1.875rem;
                        font-size: .875rem;
                        img {
                            width: .75rem;
                            height: .75rem;
                        }
                    }
                }
                .btn {
                    width: 7.375rem;
                    height: 1.875rem;
                    line-height: 1.875rem;
                    text-align: center;
                    font-size: .875rem;
                    border-radius: .25rem;
                    border: .0625rem solid #009c7f;
                    background: #ffffff;
                    cursor: pointer;
                    color: #009c7f;
                }
            }
            .title-data {
                margin-top: .625rem;
                margin-bottom: 1rem;
                &-item {
                    display: inline-block;
                    border-radius: .875rem;
                    padding: .375rem .75rem;
                    background: #fef8e9;
                    color: #ef9d19;
                    font-size: .75rem;
                    font-weight: 400;
                    margin-right: .625rem;
                }
            }
        }
        .test-content {
            height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
            box-sizing: border-box;
            overflow-y: auto;
            .test-content-ques {
                background: #ffffff;
                width: 100%;
                box-sizing: border-box;
                padding: 1.25rem 0;
                margin-bottom: .625rem;
                position: relative;
                color: #999999;
                .squre {
                    width: .875rem;
                    height: 1rem;
                    border-radius: 0 .375rem .375rem 0;
                    background: #5a85ec;
                    position: absolute;
                    top: 1.625rem;
                    left: 0;
                }
                .test-tittle,.test-body {
                    padding: 0 1.875rem;
                }
            }
        }
    }
    .right {
        padding: 1.125rem 0;
        width: 20.3125rem;
        margin-left: .625rem;
        background: #ffffff;
        box-sizing: border-box;
        height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
        .time-box {
            display: flex;
            align-items: center;
            color: #2a2b2a;
            font-size: 1.875rem;
            font-weight: 700;
            padding-left: .625rem;
            padding-bottom: 1.25rem;
            border-bottom: .0625rem dashed #eaeaea;
            .time-text {
                font-size: 1rem;
                font-weight: 700;
            }
            .time-number {
                width: 3.75rem;
                height: 3.75rem;
                line-height: 3.75rem;
                text-align: center;
                border-radius: .25rem;
                border: .0625rem solid #eaeaea;
                background: #f5f5f5;
                margin: 0 .625rem;
            }
        }
        .test-number-box {
            height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 8.125rem);
            box-sizing: border-box;
            padding: 1.25rem .625rem 1.25rem 0;
            overflow-y: scroll;
            .test-number-item {
                display: inline-block;
                margin-left: .625rem;
                margin-bottom: .625rem;
                border-radius: .25rem;
                width: 2.5rem;
                height: 2.5rem;
                line-height: 2.5rem;
                text-align: center;
                color: #2a2b2a;
                font-size: 1rem;
                font-weight: 400;
                border: .0625rem solid #eaeaea;
                background: #f5f5f5;
                box-sizing: border-box;
            }
            .disabled {
                background: #bebebe;
                cursor: not-allowed;
            }
            .blue {
                background: #5a85ec;
                color: #ffffff;
            }
            .red {
                background: #dd2a2a;
                color: #ffffff;
            }
            .green {
                background: #00c9a3;
                color: #ffffff;
            }
            .yellow {
                background: #f1be21;
                color: #ffffff;
            }
            .size285 {
                width: 17.8125rem;
                height: 2.75rem;
                font-size: 1rem;
                font-weight: 700;
                margin-left: .625rem;
                margin-top: 1.25rem;
                img {
                    width: 1rem;
                    height: 1rem;
                }
            }
        }
    }
}
.icon-btn {
    border-radius: .25rem;
    background: #00c9a3;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
        margin-right: .3125rem;
    }
}
.answer-img-box {
    padding-left: 1.875rem;
    padding-top: 1.25rem;
    .answer-img {
        width: 10.8125rem;
        height: 10.8125rem;
        border-radius: .25rem;
        margin-right: .625rem;
    }
}
:deep(.el-checkbox-group) {
    .el-checkbox {
        width: 6.25rem;
        height: 3.125rem;
        margin-right: 1.25rem;
        display: inline-flex;
        justify-content: center;
    }
    .is-checked {
        .el-checkbox__inner {
            background: #5a85ec;
            border: .0625rem solid #5a85ec;
        }
        .el-checkbox__label {
            color: #5a85ec;
        }
    }
    .el-checkbox__inner {
        &:hover {
            border: .0625rem solid #5a85ec;
        }
    }
}
.show-analyse {
    width: 100%;
    background: #fef8e9;
    padding-left: 1.875rem;
    height: 2.1875rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    color: #666666;
    font-size: .75rem;
    font-weight: 400;
    margin-top: 1.25rem;
    span {
        margin-left: .375rem;
    }
}
.analyse {
    padding: .625rem 1.875rem;
    letter-spacing: .125rem;
    background: #fef8e9;
    div {
      margin-bottom: .625rem;
    }
}
.flex-sty {
  display: flex;
  font-size: .875rem;
  align-items: baseline;
  div {
    max-width: 52.375rem;
    line-height: 1.0625rem;
  }
  span {
    text-wrap: nowrap;
    font-weight: 700;
    letter-spacing: normal;
  }
}
.paper-content-ques {
    margin-top: 1.25rem;
    border-top: .0625rem dashed #EAEAEA;
    padding: 1.25rem 1.875rem;
}

.paper-content-ques2 {
    margin-top: 1.25rem;
    border-top: .0625rem dashed #EAEAEA;
}

.selected-options {
    padding: 0.5rem 0;
    font-size: 1rem;
    font-weight: 500;
    color: #2a2b2a;
    margin-bottom: 0.5rem;
}

.padding2030 {
    padding: 1.25rem 1.875rem;
}

.tip-text {
    font-size: 0.875rem;
    color: #ff7700;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.blue-btn {
    width: 7.625rem;
    height: 2.375rem;
    line-height: 2.375rem;
    text-align: center;
    border-radius: 1.1875rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
    margin-right: .625rem;
    font-weight: 400;
}
.black-text {
    color: black!important;
    font-size: 16px;
}
.red-border {
    border: .0625rem solid #dd2a2a;
    color: black!important;
    .squre {
        background: #dd2a2a!important;
    }
    .wrong-selected {
        border: .0625rem solid #dd2a2a!important;
        background: #fce9e9!important;
        div {
            background-image: url(@/assets/img/percision/wrong-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.green-border {
    color: black!important;
    border: .0625rem solid #00C9A3;
    .squre {
        background: #00C9A3!important;
    }
    .selected {
        border: .0625rem solid #00c9a3!important;
        background: #e5f9f6!important;
        div {
            background-image: url(@/assets/img/percision/right-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.yellow-border {
    color: black!important;
    border: .0625rem solid #f1be21;
    .squre {
        background: #f1be21!important;
    }
    .selected {
        border: .0625rem solid #00c9a3!important;
        background: #e5f9f6!important;
        div {
            background-image: url(@/assets/img/percision/right-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
    .wrong-selected {
        border: .0625rem solid #dd2a2a!important;
        background: #fce9e9!important;
        div {
            background-image: url(@/assets/img/percision/wrong-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.green-box {
    border: .0625rem solid #00c9a3!important;
    background: #e5f9f6!important;
    div {
        background-image: url(@/assets/img/percision/right-check.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.red-box {
    border: .0625rem solid #dd2a2a!important;
    background: #fce9e9!important;
    div {
        background-image: url(@/assets/img/percision/wrong-check.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.yellow-box {
    border: .0625rem solid #f1be21!important;
    background: #fef8e8!important;
    div {
        background-image: url(@/assets/img/percision/harf-right.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.answers {
    display: flex;
    margin-top: 1.25rem;
    .answer-box {
        width: 6.25rem;
        height: 3.125rem;
        border-radius: .25rem;
        cursor: pointer;
        border: .0625rem solid #dddddd;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1.25rem;
        &:first-child {
            margin-left: 1.875rem;
        }
        div {
            border: .0625rem solid #999999;
            width: 1rem;
            height: 1rem;
            border-radius: .125rem;
            margin-right: .625rem;
        }
    }
}
.answers-container {
    margin-top: 1rem;
}
  .page-header {
  margin-bottom: 18px;margin-top: 20px;
}

.breadcrumbs {
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
}

.breadcrumbs .back-link {
  color: #606266;
  text-decoration: none;
  font-size: 18px;
}
.breadcrumbs .back-link:hover {
  color: #00bfa5;
}

.breadcrumb-separator {
  color: #c0c4cc;
  margin: 0 5px;
}

.breadcrumb-item {
  color: #606266;
}
.breadcrumb-item.active {
  color: #303133;
  font-weight: 500;
}


.dialog-footer {
    margin-top: 9.375rem;
    display: flex;
    justify-content: center;
}
</style>
<style lang="scss">
.answer-item:not(:last-child) {
    margin-bottom: 1.875rem;
}
.dialog-correct {
    width: 34.875rem!important;
    border-radius: 1.25rem;
    box-sizing: border-box;
}

/* 添加全局消息框样式 */
.el-message-box {
    border-radius: 0.75rem !important;
    padding-bottom: 1rem !important;
    
    .el-message-box__header {
        padding-top: 1.25rem !important;
        
        .el-message-box__title {
            font-size: 1.125rem !important;
            font-weight: 600 !important;
            text-align: center !important;
        }
    }
    
    .el-message-box__content {
        padding: 1.25rem !important;
        
        .el-message-box__message p {
            text-align: center !important;
            font-size: 1rem !important;
        }
    }
    
    .el-message-box__btns {
        display: flex !important;
        justify-content: center !important;
        padding: 0.5rem 1rem 0.5rem 1rem !important;
        
        .el-button {
            min-width: 5.5rem !important;
            border-radius: 1.25rem !important;
            font-size: 0.875rem !important;
            padding: 0.5rem 1.25rem !important;
            margin-left: 1rem !important;
            margin-right: 1rem !important;
        }
        
        .el-button--primary {
            background-color: #00c9a3 !important;
            border-color: #00c9a3 !important;
        }
    }
}
</style>