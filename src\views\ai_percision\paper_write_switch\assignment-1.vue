<template>
  <div class="assignment-report">
    <div class="report-header">
      <h2>答题报告</h2>
    </div>
    
    <div class="report-summary">
      <div class="summary-item">
        <div class="item-label">总题数</div>
        <div class="item-value">{{ reportData?.quesCount || 0 }}</div>
      </div>
      <div class="summary-item">
        <div class="item-label">正确题数</div>
        <div class="item-value">{{ reportData?.correctCount || 0 }}</div>
      </div>
      <div class="summary-item">
        <div class="item-label">正确率</div>
        <div class="item-value">{{ reportData?.correctRate || 0 }}%</div>
      </div>
      <div class="summary-item">
        <div class="item-label">答题用时</div>
        <div class="item-value">{{ formatTime(reportData?.answerTime) }}</div>
      </div>
    </div>
    
    <div class="question-result">
      <h3>答题情况</h3>
      <QuestionResultTable :questionItems="reportData?.quesItemList || []" />
    </div>
    
    <div class="difficulty-analysis" v-if="reportData?.quesDegree && reportData.quesDegree.length > 0">
      <h3>难度分析</h3>
      <el-table :data="reportData.quesDegree" border style="width: 100%">
        <el-table-column prop="degreeType" label="难度" width="100">
          <template #default="scope">
            {{ getDifficultyLabel(scope.row.degreeType) }}
          </template>
        </el-table-column>
        <el-table-column prop="quesCount" label="题量" width="100"></el-table-column>
        <el-table-column prop="quesRatio" label="占比">
          <template #default="scope">
            {{ (Number(scope.row.quesRatio) * 100).toFixed(2) }}%
          </template>
        </el-table-column>
        <el-table-column prop="quesNos" label="题号"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { assignmentsReportApi } from '@/api/assessment/report';
import QuestionResultTable from '@/components/QuestionResultTable/index.vue';

const route = useRoute();
const reportData = ref<any>(null);

onMounted(async () => {
  await fetchReportData();
});

const fetchReportData = async () => {
  try {
    const assignmentId = route.query.assignmentId as string;
    if (!assignmentId) {
      console.error('Missing assignmentId in route query');
      return;
    }
    
    const res = await assignmentsReportApi({ assignmentId });
    if (res.code === 200) {
      reportData.value = res.data;
    } else {
      console.error('Failed to fetch report data:', res.msg);
    }
  } catch (error) {
    console.error('Error fetching report data:', error);
  }
};

const formatTime = (seconds: string | number) => {
  if (!seconds) return '0秒';
  
  const totalSeconds = Number(seconds);
  if (isNaN(totalSeconds)) return '0秒';
  
  const minutes = Math.floor(totalSeconds / 60);
  const remainingSeconds = totalSeconds % 60;
  
  if (minutes === 0) {
    return `${remainingSeconds}秒`;
  } else {
    return `${minutes}分${remainingSeconds}秒`;
  }
};

const getDifficultyLabel = (type: number) => {
  switch (type) {
    case 1:
      return '容易';
    case 2:
      return '中等';
    case 3:
      return '困难';
    default:
      return '未知';
  }
};
</script>

<style lang="scss" scoped>
.assignment-report {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .report-header {
    margin-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
    
    h2 {
      font-size: 20px;
      color: #303133;
      margin: 0;
    }
  }
  
  .report-summary {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
    
    .summary-item {
      text-align: center;
      
      .item-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 5px;
      }
      
      .item-value {
        font-size: 24px;
        color: #303133;
        font-weight: bold;
      }
    }
  }
  
  .question-result,
  .difficulty-analysis {
    margin-bottom: 30px;
    
    h3 {
      font-size: 16px;
      color: #303133;
      margin-bottom: 15px;
      font-weight: 500;
    }
  }
}
</style>