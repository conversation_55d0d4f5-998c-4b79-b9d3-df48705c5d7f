<!-- 名师课堂-章节列表 -->
<template>
  <div class="content">

    <div class="inner">
     <!-- 顶部导航 -->
    <header class="page-header">
      <div class="breadcrumbs">
        <a href="#" class="back-link" @click.prevent="goBack">&lt; 返回</a>
        <span class="breadcrumb-separator">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
        <span class="breadcrumb-item">名师同步学</span>
        <!-- <span class="breadcrumb-separator">&nbsp;>&nbsp;</span> -->
        <!-- <span class="breadcrumb-item">{{ state.subName }} </span> -->
        <span class="breadcrumb-separator">&nbsp;>&nbsp;</span>
        <span class="breadcrumb-item active">{{query?.courseTitle }}</span>
      </div>
    </header>
      <div class="wrap">
        <div class="menu_lt">
          <!-- 选中第一节变色，2或3末级变绿，中间不变 -->
          <div class="menu_ul" id="menu_ul" ref="quesBoxRef" @scroll="handleScroll">
            <div class="menu_li" v-for="(item,index) in state.chapterList" :key="index">
              <!-- 第1节 -->
              <div class="menu1" :class="item.active" @click="tog3Menu1" :data-i="index">
                <!-- 督学任务标签 -->
                <div class="task-badge" v-if="item.task"></div>
                <div class="nowrap">{{cleanMenuText(item.chapterName)}}</div>
                <img src="@/assets/img/teachroom/down.svg" class="menu_img" :class="item.up"
                  v-if="item.children.length" />
              </div>
              <!-- 第2节 -->
              <div class="child" v-show="item.up" v-for="(item2,index2) in item.children" :key="index2">
                <div class="menu2" :class="item2.active" @click="tog3Menu2" :data-i="index" :data-i2="index2"
                  :style="item2.children.length?'background:none;':''">
                  <!-- 督学任务标签 -->
                  <div class="task-badge" v-if="item2.task"></div>
                  <div class="nowrap" :style="item2.children.length?'color:#333;':''">{{cleanMenuText(item2.chapterName)}}</div>
                  <img src="@/assets/img/teachroom/down.svg" class="menu_img" :class="item2.up"
                    v-if="item2.children.length" />
                </div>
                <!-- 第3节 -->
                <div class="menu3" v-show="item2.up" v-for="(item3,index3) in item2.children" :key="index3">
                  <div class="menu3-item" :class="item3.active" @click="tog3Menu3" :data-i="index" :data-i2="index2"
                    :data-i3="index3">
                    <!-- 督学任务标签 -->
                    <div class="task-badge" v-if="item3.task" style="width: 38px;height: 24px;"></div>
                    <div class="nowrap">{{cleanMenuText(item3.chapterName)}} 
                      <span v-if="item3.task" style="color: red; font-size: 12px;">[任务]</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="menu_rt" id="menu_rt" ref="quesBoxRef2" @scroll="handleScroll2">
          <div class="book">
            <div class="book_ver" @click="showBookModal" v-if="!query?.source">
              <img style="margin-right: 10px;"  width="24px" height="24px" src="@/assets/img/percision/switch.png" />
              
              <span>当前教材：</span><span style="color: rgba(0, 156, 127, 1);text-decoration: underline;cursor: pointer;" >{{ state.subName }} {{state.bookName}}</span>
            </div>
            <div class="book_ver"  v-else>
              <img style="margin-right: 10px;"  width="24px" height="24px" src="@/assets/img/percision/switch.png" />
              <span>当前教材：</span><span  >{{state.bookName}}</span>
            </div>
            <div class="book_fav" @click="goCollect">
              <img src="@/assets/img/teachroom/star.svg" />我的收藏
            </div>
          </div>
          <!-- {{ state }} -->
          <div class="wk_wrap" v-if="state.isShow&&state.data.length">
            
            <div class="wk_box" v-for="(item,index) in state.data">
              <div class="wk_h1">
                <span></span><span v-html="EnhancedReplaceMathString(item.name)"></span>
              </div>
              <div class="wk_ul" >
                <!-- 带概述 -->
                <!-- <div class="wk_li wk_li2" v-for="(item3,index3) in item2.videoInfos" v-if="item2.videoInfos">
                  <div class="wk_pic" @click="wekePlay" :data-i="index" :data-i2="index2" :data-i3="index3">
                    <img :src="item3.cover" class="wk_img" v-if="item3.cover" style="transform: scale(1.1);" />
                    <img src="@/assets/img/teachroom/novid2.png" class="wk_img" v-else />
                    <img src="@/assets/img/teachroom/play.svg" class="wk_play" />
                  </div>
                  <div class="wk_bom">
                    <div class="wk_tit nowrap" @click="wekePlay" :data-i="index" :data-i2="index2" :data-i3="index3"
                      v-html="EnhancedReplaceMathString(item3.videoName)">
                    </div>
                    <div class="wk_state" @click="wekePlay" :data-i="index" :data-i2="index2" :data-i3="index3">
                      <div class="wk_status status2" v-if="item3.studyStatus==2">已学完</div>
                      <div class="wk_status status1" v-else-if="item3.studyStatus==1">未学完</div>
                      <div class="wk_status status0" v-else>未学习</div>
                      <div class="wk_thumbs">
                        <img src="@/assets/img/teachroom/thumbs.svg" />
                        <img src="@/assets/img/teachroom/thumbs2sel.svg" />
                        {{item3.likeNum||0}}
                      </div>
                    </div>
                  </div>
                </div> -->
                <!-- 无视频带概述 -->
                <!-- <div class="wk_li wk_li2" >
                  <div class="wk_pic">
                    <img src="@/assets/img/teachroom/novid2.png" class="wk_img" />
                  </div>
                  <div class="wk_bom">
                    <div class="wk_tit nowrap">{{item2.knowledgeName}}</div>
                  </div>
                </div> -->
                <!-- 例题 -->
                <div class="wk_li"  @click="wekePlay2(item2,item)" v-for="(item2,index2) in item.videoList" :key="index2">
                  <!-- {{ item2?.task }} -->
                  <!-- 语文同步学习特殊样式 -->
                  <div class="wk_pic chinese-sync-container" v-if="isChineseSyncStyle||isEnglishSyncStyle">
                    <div class="task-img-container" v-if="item2?.task">
                      <img src="@/assets/img/synchronous/renwu.png" class="task-img" />
                    </div>
                    <div class="chinese-sync-card" >
                      <div class="english-sync-title" v-if="isEnglishSyncStyle" :style="{ backgroundImage: getSyncBackgroundImage }" v-html="EnhancedReplaceMathString(item2.title)"></div>

                      <div class="chinese-sync-title" v-else :style="{ backgroundImage: getSyncBackgroundImage }" v-html="EnhancedReplaceMathString(item2.title)"></div>
                    </div>
                    <div class="view chinese-sync-view" v-if="item2.introduction" @click.stop="showTeacherDetail(item2)"> 查看老师详情</div>
                    <img src="@/assets/img/teachroom/play.svg" class="wk_play chinese-sync-play" />
                  </div>
                  <!-- 普通样式 -->
                  <div class="wk_pic" v-else>
                    <div class="view" v-if="item2.introduction" @click.stop="showTeacherDetail(item2)"> 查看老师详情</div>
                    <!-- {{ item2?.tas }} -->
                    <div class="task-img-container" v-if="item2?.task">
                      <img src="@/assets/img/synchronous/renwu.png" class="task-img" />
                    </div>
                    <img :src="item2.videoCover" class="wk_img" v-if="item2.videoCover" />
                    <img src="@/assets/img/teachroom/novid2.png" class="wk_img" v-else />
                    <img src="@/assets/img/teachroom/play.svg" class="wk_play" />
                  </div>
                  <div class="wk_bom">
                    <!-- {{ item2 }} -->
                      
                    <div class="wk_tit nowrap" v-html="EnhancedReplaceMathString(item2.title)" v-if="!isChineseSyncStyle"></div>
                    <div class="wk_state">
                      <div class="wk_status status2" v-if="item2.status==2">已学完</div>
                      <div class="wk_status status1" v-else-if="item2.status==1">未学完</div>
                      <div class="wk_status status0" v-else>未学习</div>
                      <div class="wk_thumbs">
                        <img src="@/assets/img/teachroom/thumbs.svg" />
                        <img src="@/assets/img/teachroom/thumbs2sel.svg" />
                        {{item2.likeNum||0}}
                      </div>
                      <!-- <div class="wk_collect active">
                          <img src="@/assets/img/teachroom/collect.svg" />
                          <img src="@/assets/img/teachroom/collectsel.svg" />
                        </div> -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 无数据 -->
          <div class="nodata" v-if="state.isShow&&!state.data.length">
            <img src="@/assets/img/user/nodata.png" />该章节暂无名师同步学课程！
          </div>
        </div>
      </div>
    </div>
  </div>
  <buyVip :show="state.showVip" @close="state.showVip = false"></buyVip>
  
    <!-- 老师详情弹窗 -->
    <el-dialog
      v-model="state.showTeacherModal"
      width="1200px"
      :show-close="false"
      :modal="true"
      :close-on-click-modal="false"
      class="teacher-detail-dialog"
    >
      <template #header>
                <div class="dialog-header">
          <div class="dialog-title">
            <span class="title-text">老师信息</span>
          </div>
          <el-icon class="close-icon" @click="closeTeacherModal">
            <Close />
          </el-icon>
        </div>
      </template>

      <div class="teacher-detail-content">
        <!-- 加载状态 -->
        <div class="teacher-loading" v-if="state.teacherLoading">
          <div class="loading-spinner"></div>
          <p>正在加载老师信息...</p>
        </div>
        
        <!-- 老师介绍图片 -->
        <div class="teacher-info-container" v-else>
          <div class="teacher-info-wrapper">
            <iframe :src="state.currentTeacher?.introduction" width="100%" height="600"></iframe>
            <!-- <img 
              :src="state.currentTeacher?.introduction" 
              :alt="state.currentTeacher?.title || '老师信息'"
              @error="handleTeacherImageError"
              @click="openImagePreview"
              /> -->
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 切换教材弹窗 -->
    <el-dialog
      v-model="state.showBookModal"
      width="1090px"
      height="660px"
      :show-close="false"
      :modal="true"
      :close-on-click-modal="false"
      class="book-modal-dialog"
    >
      <template #header>
        <div class="dialog-header">
          <div class="dialog-title">
            <span class="title-text">切换教材</span>
          </div>
          <el-icon class="close-icon" @click="closeBookModal">
            <Close />
          </el-icon>
        </div>
      </template>

      <div class="book-modal-content">
        <!-- 加载状态 -->
        <div class="book-loading" v-if="state.bookModalLoading">
          <div class="loading-spinner"></div>
          <p>正在加载教材数据...</p>
        </div>
        
        <!-- 教材内容 -->
        <div v-else>
          <!-- 教材版本选择 -->
          <div class="edition-selector" style="width: 1020px;">
            <el-select 
              v-model="state.selectedEdition" 
              placeholder="请选择教材版本" 
              class="edition-select subkey" 
              @change="selectEdition">
              <template #label="{ label, value }">
                  <label class="selector-label">教材版本：</label>
                  <span style="font-weight: bold">{{ label }}</span>
                </template>
              <el-option 
                v-for="edition in state.editionList" 
                :key="edition.idYxp" 
                :label="edition.name" 
                :value="edition.idYxp">
              </el-option>
            </el-select>
          </div>
          
          <!-- 教材书本列表 -->
          <div class="book-list-container">
            <div class="book-grid" v-if="state.filteredBookList.length > 0">
              <div class="book-item" 
                   v-for="book in state.filteredBookList" 
                   :key="book.bookId"
                   @click="selectBook(book)"
                   :class="{'book-item-active':state.currentBookId== book.bookId}">
                <div class="book-cover">
                  <img v-if="book.cover || book.coverUrl" :src="book.cover || book.coverUrl" :alt="book.name || book.bookName" @error="handleImageError" />
                  <img v-else src="@/assets/img/synchronous/jiaocai.png" alt="">
                </div>
                <div class="book-info">
                  <div class="book-name">{{book.editionName}}{{book.subject}}{{book.gradeName}}{{book.termName}}</div>
                  <!-- <div class="book-name">{{book.editionName}}</div> -->
                  <!-- <div class="book-desc">{{book.description || book.desc || ''}}</div> -->
                </div>
                <div class="current-book-badge" v-if="query.bookId == book.bookId">
                  <span>当前教材</span>
                  
                </div>
              </div>
            </div>
            
            <div class="no-books" v-else>
              <img src="@/assets/img/user/nodata.png" />
              <p>暂无教材数据</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 弹窗底部提示 -->
      <!-- <div class="book-modal-footer">
        <p class="modal-hint">点击教材封面可切换到对应教材，按ESC键关闭弹窗</p>
      </div> -->
    </el-dialog>

</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, watch, onUnmounted, computed } from 'vue';
  import router from '@/router/index'
  import { useUserStore } from "@/store/modules/user"
  import { ElMessage } from "element-plus"
  import { Close } from '@element-plus/icons-vue'
  import { subjectEnum, subjectEnum2, subjectList } from '@/utils/user/enum'
  import { deepClone, ReplaceMathString } from '@/utils/user/util'
  import { getBookChapterApi, getVideoListApi, getVideoReviewNumApi, setUserVideoViewNumberApi,
    getVideoListYxpApi  //new 视频
  } from "@/api/video"
  import { useRoute } from "vue-router"
  import buyVip from "@/views/components/buyVip/index.vue"
  import { setLearnKey } from '@/utils/user/learntime'
  import { useRouteStoreHook } from '@/store/modules/route'
  import ywfont from '@/assets/img/synchronous/ywfont.png'
  import yyfont from '@/assets/img/synchronous/yyfont.png'
  import { enhanceStringProcessor, cleanMenuText } from '@/utils/htmlCleaner'

   
  // new   左侧章节
  import { getChapterListYxpApi,
    getEditionList_YxprApi,    //获取教材版本列表_yxp
    getBookList_YxpApi     //获取教材列表_yxp
  } from "@/api/book"

  // Define EnhancedReplaceMathString function
  const EnhancedReplaceMathString = (str: string): string => {
    return enhanceStringProcessor(str, ReplaceMathString);
  };
  
  defineOptions({
    name: "SynchronousList"
  })
  const idYxp = ref()
  const bookId = ref()
  const route = useRoute()
  const query = reactive<any>(route.query)
  
  const videoList: any = ref()
  const state : any = reactive({
    showVip: false,
    bookName: '',
    termNameNow: '',
    isShowContent: true,
    loading: true,
    subList: [],
    subActive: '',
    subName: '语文',
    learnNow: {},
    subObject: {},
    sessionObject: {},
    childLen: 0, //章节层级数
    chapterList: [], //课程列表
    chapterArr: [], //课程列表-所有学科
    sortArr: ['语文', '数学', '英语', '物理', '化学', '生物'], // '历史', '道法', '地理', '科学' 初中没有科学
    sortArr2: ['语文', '数学', '英语'], //, '科学'
    isShow: 0,
    isFirst: 1,
    chapterId: '',
    chapterName: '',
    data: [],
    isRefresh: 0,
    scrollTop: 0,
    scrollTop2: 0,
    // 老师详情弹窗相关状态
    showTeacherModal: false,
    currentTeacher: null,
    teacherLoading: false,
    iframeError: false,
    
    // 切换教材弹窗相关状态
    showBookModal: false,
    editionList: [], // 教材版本列表
    bookList: [], // 教材书本列表
    selectedEdition: '', // 选中的教材版本ID
    filteredBookList: [], // 过滤后的书本列表
    bookModalLoading: false,
    currentBookId: query.bookId || '' // 当前选中的教材ID
  })

  const resourceIds: any = computed(() => { 
    const resourceIds = route.query.resourceIds || '';
    if (!resourceIds || typeof resourceIds !== 'string') return false;
    
    // 将逗号分隔的章节ID转为数组并检查是否包含当前章节ID
    const chapterIdsArray = resourceIds.split(',');
    return chapterIdsArray
  })
  // 计算属性：判断是否为语文同步学习样式
  const isChineseSyncStyle = computed(() => {
    const subjectType = route.query.subjectType || route.query.subName
    const moduleType = route.query.moduleType
    
    // 判断是否为语文科目
    const isChineseSubject = subjectType?.includes('chinese')
    
    // 判断是否为同步学习模块
    const isSyncModule = moduleType === '2'
    
    const isChineseSync = isChineseSubject && isSyncModule
    
    return isChineseSync
  })

    // 计算属性：判断是否为语文同步学习样式
    const isEnglishSyncStyle = computed(() => {
    const subjectType = route.query.subjectType || route.query.subName
    const moduleType = route.query.moduleType
    
    // 判断是否为语文科目
    const isEnglishSubject = subjectType?.includes('english')
    
    // 判断是否为同步学习模块
    const isSyncModule = moduleType === '3'
    
    const isEnglishSync = isEnglishSubject && isSyncModule
    
    return isEnglishSync
  })

  // 计算属性：获取背景图片
  const getSyncBackgroundImage = computed(() => {
    const subjectType = route.query.subjectType || route.query.subName
    const isChineseSubject = subjectType?.includes('chinese')
    const isEnglishSubject = subjectType?.includes('english')
    
    if (isChineseSubject) {
      return `url(${ywfont})`
    } else if (isEnglishSubject) {
          return `url(${yyfont})`
     }
   })

  // 判断是否是任务章节
  const isTaskChapter = (chapterId: string) => {
    const yxpChapterIds = route.query.yxpChapterIds || '';
    if (!yxpChapterIds || typeof yxpChapterIds !== 'string') return false;

    // 将逗号分隔的章节ID转为数组并检查是否包含当前章节ID
    const chapterIdsArray = yxpChapterIds.split(',');
    return chapterIdsArray?.includes(chapterId);
    
  }

    // 自定义返回方法
  const customGoBack = () => {
      router.go(-1)
  }
onMounted(() => {
    init()
    
    // 检查是否有保存的状态需要恢复
    const savedChapterId = sessionStorage.getItem('syncListChapterId');
    const savedScrollTop = sessionStorage.getItem('syncListScrollTop');
    const savedScrollTop2 = sessionStorage.getItem('syncListScrollTop2');
    
    if (savedChapterId && savedScrollTop && savedScrollTop2) {
      // 将字符串转换为数字
      const scrollTop = parseInt(savedScrollTop, 10);
      const scrollTop2 = parseInt(savedScrollTop2, 10);
      
      // 设置为来自视频页面的标志，以便getCourseList使用保存的滚动位置
      query.fromVideo = 'true';
      query.selectedChapterId = savedChapterId;
      
      // 在DOM更新后恢复滚动位置
      setTimeout(() => {
        setTop(scrollTop);
        setTop2(scrollTop2);
        
        // 滚动到选中的章节节点
        scrollToActiveChapter();
        
        // 恢复完成后清除保存的状态
        sessionStorage.removeItem('syncListChapterId');
        sessionStorage.removeItem('syncListScrollTop');
        sessionStorage.removeItem('syncListScrollTop2');
      }, 300);
    }
    
    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeyDown);
    window.customGoBack = customGoBack
  })

  onUnmounted(() => {
    // 移除键盘事件监听
    document.removeEventListener('keydown', handleKeyDown);
        // 清除自定义返回方法
    if (window.customGoBack) {
      delete window.customGoBack
    }
  })

  // 处理键盘按下事件
  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      if (state.showTeacherModal) {
        closeTeacherModal();
      } else if (state.showBookModal) {
        closeBookModal();
      }
    }
  }


  //记录滚动位置
  const quesBoxRef = ref(null);
  const handleScroll = () => {
    const element : any = quesBoxRef.value;
    if (element) {
      setData({
        scrollTop: element.scrollTop
      })
    }
  }
  const quesBoxRef2 = ref(null);
  const handleScroll2 = () => {
    const element : any = quesBoxRef2.value;
    if (element) {
      setData({
        scrollTop2: element.scrollTop
      })
    }
  }

  // 属性赋值
  const setData = (obj : any) => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        state[key] = obj[key]
      }
    }
  }
  //返回置顶
  const setTop = (num:any) => {
    const div : any = document.getElementById('menu_rt');
    if (div) {
      div.scrollTop = num;
    }
  }

  //返回置顶
  const setTop2 = (num : any) => {
    const div : any = document.getElementById('menu_ul');
    if (div) {
      div.scrollTop = num;
    }
  }
  
  // 滚动到选中的章节节点
  const scrollToActiveChapter = () => {
    // 延迟执行以确保DOM已经更新
    setTimeout(() => {
      // 获取左侧菜单容器
      const menuContainer = document.getElementById('menu_ul');
      if (!menuContainer) return;
      
      // 尝试查找不同级别的活跃节点
      let activeNode: Element | null = null;
      
      // 优先尝试查找二级或三级活跃节点（更具体的选择）
      const activeLower = menuContainer.querySelector('.menu2.active, .menu3 .active');
      if (activeLower) {
        activeNode = activeLower;
      } else {
        // 如果没有找到二级/三级节点，则查找一级节点
        const activeFirst = menuContainer.querySelector('.menu1.active');
        if (activeFirst) {
          activeNode = activeFirst;
        }
      }
      
      // 如果找不到任何活跃节点，直接返回
      if (!activeNode) return;
      
      // 计算节点位置
      const containerRect = menuContainer.getBoundingClientRect();
      const nodeRect = activeNode.getBoundingClientRect();
      
      // 检查节点是否在可视区域内
      const isVisible = (
        nodeRect.top >= containerRect.top &&
        nodeRect.bottom <= containerRect.bottom
      );
      
      // 如果节点不在可视区域内，则滚动到该节点
      if (!isVisible) {
        // 计算滚动位置，使节点位于容器中间偏上位置
        const scrollTop = nodeRect.top + menuContainer.scrollTop - containerRect.top - (containerRect.height / 3);
        
        // 平滑滚动到目标位置
        menuContainer.scrollTo({
          top: scrollTop,
          behavior: 'smooth'
        });
        
        // console.log('滚动到选中章节');
      }
    }, 300); // 给DOM足够的时间更新
  }

const init = () => {
    //重置数据
    setData({
      subList: [],
      subName: query.subName || '语文',
      subActive: query?.subject||'chinese3',
      learnNow: {},
      subObject: {},
      sessionObject: {},
      chapterList: []
    })

    const learnNow = JSON.parse(localStorage.learnNow || '{}')
    //无用户返回首页
    if (!learnNow) {
      return
    }
    const gradeId = learnNow?.gradeId || ''
    //匹配科目传参
    setData({
      learnNow,
      gradeId,
      termNameNow: learnNow.termId,
      subObject: learnNow.versions
    })
    if (gradeId > 9) {
      //高中跳学大知识点
      router.replace({ name: "PointRoomIndex" })
    } else {
      setSubList()
    }
  }

  // 判断会员状态
  const getIsMember = () => {
    const isVip = JSON.parse(localStorage.memberInfo || '{}')
    if (isVip?.isMember) {
      return true
    } else {
      state.showVip = true
      return false
    }
  }

  //获取课程章节
const getCourseList = (id ?:string) => {
    const { subObject, subActive, chapterArr } = state
    // const { bookId } = subObject[subActive]

    const  isBookId  = id||query.bookId

    // if (chapterArr[bookId]) {
    //   //有缓存
    //   setData({
    //     chapterList: chapterArr[bookId],
    //     chapterArr,
    //     isShow: 1
    //   })
    //   const child : any = [0, 0, 0]
    //   const list = chapterArr[bookId]
    //   if (list.length) {
    //     // 默认展开
    //     for (const i of list) {
    //       child[0] = 1
    //       for (const n of i.children) {
    //         child[1] = 1
    //         for (const m of n.children) {
    //           child[2] = 1
    //         }
    //       }
    //     }
    //     setData({
    //       childLen: child[0] + child[1] + child[2]
    //     })
    //   }
    // } else {
    //没缓存
    // 保存当前滚动位置
    const currentScrollTop = state.scrollTop;
    const currentScrollTop2 = state.scrollTop2;
    
    setData({
      isShow: 0,
      chapterList: []
    })

    let isCourseTitle;

    if(query.courseTitle == '同步阅读课'){
      isCourseTitle = 1
    }else if(query.courseTitle == '同步作文课'){
      isCourseTitle = 2
    }else{
      isCourseTitle=''
    }

    // console.log(String(query.yxpChapterIds),"query.chapterIds")
    
// return
    const param = {
      // bookId,
      bookId:isBookId,
      // hierarchy: 3
      isSync:isCourseTitle,
      chapterIds:query.yxpChapterIds?query.yxpChapterIds:''
    }
    getChapterListYxpApi(param)
    // getBookChapterApi(param)
      .then((res : any) => {
        const list = res.data
        const child : any = [0, 0, 0]
        if (list.length) {
          // 默认展开并设置任务标识
          for (const i of list) {
            i.active = ''
            i.up = 'up'
            // 设置第一级章节的任务标识
            i.task = isTaskChapter(i.chapterId)
            // 调试信息
            if (i.task) {
              console.log(`第一级章节 "${i.chapterName}" (ID: ${i.chapterId}) 是任务章节`);
            }
            child[0] = 1
            for (const n of i.children) {
              n.active = ''
              n.up = 'up'
                             // 设置第二级章节的任务标识
               n.task = isTaskChapter(n.chapterId)
               // 调试信息
               if (n.task) {
                 console.log(`第二级章节 "${n.chapterName}" (ID: ${n.chapterId}) 是任务章节`);
               }
              child[1] = 1
              for (const m of n.children) {
                m.active = ''
                m.up = 'up'
                // 设置第三级章节的任务标识
                m.task = isTaskChapter(m.chapterId)
                // 调试信息
                if (m.task) {
                  console.log(`第三级章节 "${m.chapterName}" (ID: ${m.chapterId}) 是任务章节`);
                }
                child[2] = 1
              }
            }
          }
          
          // 检查是否有从视频页面传来的选中章节ID
          const selectedChapterId = query.selectedChapterId || (route.query.fromVideo === 'true' ? state.chapterId : null);
          let chapterId = '', chapterName = '';
          let foundSelectedChapter = false;
          
          // 检查是否有任务章节需要自动选中
          const yxpChapterIds = route.query.yxpChapterIds;
          let hasTaskChapters = false;
          let firstTaskChapterId = '';
          let firstTaskChapterName = '';
          let firstTaskChapterFound = false;
          let taskChapterI = -1;
          let taskChapterI2 = -1;
          let taskChapterI3 = -1;
          
          if (yxpChapterIds && typeof yxpChapterIds === 'string' && !foundSelectedChapter) {
            const taskChapterIdsArray = yxpChapterIds.split(',');
            
            // 查找第一个任务章节
            outerLoop:
            for (let i = 0; i < list.length; i++) {
              // 检查一级章节
              if (taskChapterIdsArray.includes(list[i].chapterId)) {
                firstTaskChapterId = list[i].chapterId;
                firstTaskChapterName = list[i].chapterName;
                firstTaskChapterFound = true;
                taskChapterI = i;
                break outerLoop;
              }
              
              // 检查二级章节
              for (let j = 0; j < list[i].children.length; j++) {
                if (taskChapterIdsArray.includes(list[i].children[j].chapterId)) {
                  firstTaskChapterId = list[i].children[j].chapterId;
                  firstTaskChapterName = list[i].children[j].chapterName;
                  firstTaskChapterFound = true;
                  taskChapterI = i;
                  taskChapterI2 = j;
                  break outerLoop;
                }
                
                // 检查三级章节
                for (let k = 0; k < list[i].children[j].children.length; k++) {
                  if (taskChapterIdsArray.includes(list[i].children[j].children[k].chapterId)) {
                    firstTaskChapterId = list[i].children[j].children[k].chapterId;
                    firstTaskChapterName = list[i].children[j].children[k].chapterName;
                    firstTaskChapterFound = true;
                    taskChapterI = i;
                    taskChapterI2 = j;
                    taskChapterI3 = k;
                    break outerLoop;
                  }
                }
              }
            }
            
            // 如果找到了任务章节，选中并激活它
            if (firstTaskChapterFound) {
              // 重置所有章节的active状态
              for (let i = 0; i < list.length; i++) {
                list[i].active = '';
                for (let j = 0; j < list[i].children.length; j++) {
                  list[i].children[j].active = '';
                  for (let k = 0; k < list[i].children[j].children.length; k++) {
                    list[i].children[j].children[k].active = '';
                  }
                }
              }
              
              // 设置找到的任务章节为激活状态
              list[taskChapterI].active = 'active';
              if (taskChapterI2 !== -1) {
                list[taskChapterI].children[taskChapterI2].active = 'active';
              }
              if (taskChapterI3 !== -1) {
                list[taskChapterI].children[taskChapterI2].children[taskChapterI3].active = 'active';
              }
              
              chapterId = firstTaskChapterId;
              chapterName = firstTaskChapterName;
              foundSelectedChapter = true;
            }
          }
          
          if (selectedChapterId && !foundSelectedChapter) {
            // 尝试在章节列表中找到匹配的章节ID
            for (let i = 0; i < list.length; i++) {
              // 检查一级章节
              if (list[i].chapterId === selectedChapterId) {
                list[i].active = 'active';
                chapterId = list[i].chapterId;
                chapterName = list[i].chapterName;
                foundSelectedChapter = true;
                break;
              }
              
              // 检查二级章节
              for (let j = 0; j < list[i].children.length; j++) {
                if (list[i].children[j].chapterId === selectedChapterId) {
                  list[i].active = 'active';
                  list[i].children[j].active = 'active';
                  chapterId = list[i].children[j].chapterId;
                  chapterName = list[i].children[j].chapterName;
                  foundSelectedChapter = true;
                  break;
                }
                
                // 检查三级章节
                for (let k = 0; k < list[i].children[j].children.length; k++) {
                  if (list[i].children[j].children[k].chapterId === selectedChapterId) {
                    list[i].active = 'active';
                    list[i].children[j].active = 'active';
                    list[i].children[j].children[k].active = 'active';
                    chapterId = list[i].children[j].children[k].chapterId;
                    chapterName = list[i].children[j].children[k].chapterName;
                    foundSelectedChapter = true;
                    break;
                  }
                }
                
                if (foundSelectedChapter) break;
              }
              
              if (foundSelectedChapter) break;
            }
          }
          
          // 如果没有找到选中的章节，则默认选中第一个
          if (!foundSelectedChapter) {
            //默认第一个选中
            list[0].active = 'active'
            chapterId = list[0].chapterId
            chapterName = list[0].chapterName
            if (list[0].children.length) {
              let arr2 = list[0].children[0]
              chapterId = arr2.chapterId
              chapterName = arr2.chapterName
              list[0].children[0].active = 'active'
              if (list[0].children[0].children.length) {
                let arr3 = list[0].children[0].children[0]
                chapterId = arr3.chapterId
                chapterName = arr3.chapterName
                list[0].children[0].children[0].active = 'active'
              }
            }
          }
          
          setData({
            childLen: child[0] + child[1] + child[2],
            chapterId: chapterId,
            chapterName: chapterName,
          })
          
          // 如果是从视频页面返回或找到了任务章节，保持原有滚动位置
          if (route.query.fromVideo === 'true' || firstTaskChapterFound) {
            setTimeout(() => {
              setTop(currentScrollTop2)
              setTop2(currentScrollTop)
              // 滚动到选中的章节节点
              scrollToActiveChapter()
            }, 100)
          } else {
            setTop(0)
          }
          
          getVideoList(id)
        }
        //缓存所有学科章节
        // chapterArr[bookId] = list
        setData({
          chapterList: list,
          chapterArr,
          isShow: 0
        })
      })
      .catch(() => {
        setData({
          isShow: true,
          chapterList: [],
          data: []
        })
      })
    // }
  }

  // 1级菜单切换-数学
  const tog3Menu1 = (e : any) => {
    const { i } = e.currentTarget.dataset
    const { chapterList } = state
    const child = chapterList[i]
    if (child?.children.length) {
      //有子级
      chapterList[i].up = child.up ? '' : 'up'
      setData({
        chapterList
      })
    } else {
      //无子级
      const chapterId = child.chapterId, chapterName = child.chapterName
      //选中变色
      let list = chapterList
      for (const x of list) {
        x.active = ''
        for (const y of x.children) {
          y.active = ''
          for (const z of y.children) {
            z.active = ''
          }
        }
      }
      list[i].active = 'active'
      setData({
        chapterList: list,
        chapterId: chapterId,
        chapterName: chapterName
      })
      getVideoList()
      setTop(0)
    }
  }
  // 2级菜单切换-数学
  const tog3Menu2 = (e : any) => {
    const { i, i2 } = e.currentTarget.dataset
    const { chapterList } = state
    const child = chapterList[i]?.children
    if (child[i2]?.children?.length) {
      //有子级
      chapterList[i].children[i2].active = child[i2].active ? '' : 'active'
      chapterList[i].children[i2].up = child[i2].up ? '' : 'up'
      setData({
        chapterList
      })
    } else {
      //无子级
      const chapterId = child[i2].chapterId, chapterName = child[i2].chapterName
      //选中变色
      let list = chapterList
      for (const x of list) {
        x.active = ''
        for (const y of x.children) {
          y.active = ''
          for (const z of y.children) {
            z.active = ''
          }
        }
      }
      list[i].active = 'active'
      list[i].children[i2].active = 'active'
      setData({
        chapterList: list,
        chapterId: chapterId,
        chapterName: chapterName
      })
      getVideoList()
      setTop(0)
    }
  }

  // 3级菜单切换-数学
  const tog3Menu3 = (e : any) => {
    const { i, i2, i3 } = e.currentTarget.dataset
    const { chapterList } = state
    const arr = chapterList[i].children[i2].children[i3]
    const chapterId = arr.chapterId, chapterName = arr.chapterName
    let list = chapterList
    for (const x of list) {
      x.active = ''
      for (const y of x.children) {
        y.active = ''
        for (const z of y.children) {
          z.active = ''
        }
      }
    }
    list[i].active = 'active'
    list[i].children[i2].active = 'active'
    list[i].children[i2].children[i3].active = 'active'
    setData({
      chapterList: list,
      chapterId: chapterId,
      chapterName: chapterName
    })
    getVideoList()
    setTop(0)
  }

  //获取知识点视频列表
  const getVideoList = (id?:string) => {
    const isBookId  = id||query.bookId
    const moduleType= query.moduleType || 1
    const { chapterId, subActive } = state
    const subject = subjectList[subActive].key
    const param = {
      subject,
      // chapterName:state.chapterName,
      type: 1,
      moduleType: moduleType,
      sourceId:chapterId,
      // bookId:isBookId,
      // videoIds: []
      videoIds: resourceIds.value ? resourceIds.value : []
      
    }
    getVideoListYxpApi(param)
    // getVideoListApi(param)
      .then((res : any) => {
        const res2 = res.data
        // setData({
        //     data: res2,
        //     isShow: 1,
        //     subject
        //   })
        if (res2?.xypVideos?.length>0) {

          // for (const i of res2) {
          //   console.log(2222222222)

          // }

          //   let num = 1
          //   for (const x of res2.xypVideos) {
          //     x.num = num
          //     num++
          //     if (x.videoList) {
          //       //例题
          //       const name = x.videoList[0]?.module || ''
          //       for (const y of x.videoList) {
          //         //知识点(例题)-收藏用
          //         y['videoName3'] = `${name}(${y.title})`
          //       }
          //     }
          //   }
          // console.log("res2---",res2)
          res2.xypVideos=res2?.xypVideos?.map(item => { 
            return {...item,name:res2.chapterName,chapterId:res2.chapterId}
          })
          setData({
            data: res2.xypVideos,
            isShow: 1,
            subject
          })
        } else {
          setData({
            title: '',
            data: [],
            videoId: '',
            isShow: 1
          })
        }
      })
  }

  //我的收藏
  const goCollect = () => {
    const { subName, subActive } = state
    router.push({ name: "synchronousCollect", query: { subName, subKey: subActive } })
  }

    //显示老师详情弹窗
  const showTeacherDetail = (teacherData: any) => {
    // 防止重复点击
    if (state.showTeacherModal) {
      return;
    }
   
    // 显示弹窗和加载状态
    setData({
      showTeacherModal: true,
      teacherLoading: true,
      currentTeacher: teacherData,
      iframeError: false
    });
    
    // 模拟加载过程
    setTimeout(() => {
      setData({
        teacherLoading: false
      });
    }, 500); // 0.5秒后显示内容
  }

  //关闭老师详情弹窗
  const closeTeacherModal = () => {
    setData({
      showTeacherModal: false,
      currentTeacher: null,
      teacherLoading: false,
      iframeError: false
    });
    
    // 清理可能存在的错误提示元素
    const errorMessages = document.querySelectorAll('.image-error-message');
    errorMessages.forEach(el => el.remove());
  }

  // iframe加载完成
  const onIframeLoad = () => {
    setData({
      iframeError: false
    })
  }

  // iframe加载错误
  const onIframeError = () => {
    console.error('老师详情页面加载失败')
    setData({
      iframeError: true
    })
  }

  // 重新加载iframe
  const reloadIframe = () => {
    setData({
      iframeError: false,
      teacherLoading: true
    })
    
    // 延迟重置加载状态，模拟重新加载过程
    setTimeout(() => {
      setData({
        teacherLoading: false
      })
    }, 1000)
  }

  // 显示切换教材弹窗
  const showBookModal = () => {
    setData({
      showBookModal: true,
      selectedEdition: '全部', // 默认选择"全部"
      bookModalLoading: true,
    });
    
    // 获取教材版本列表和书本列表
    getEditionListAndBooks();
  }

  // 获取教材版本列表和书本列表
  const getEditionListAndBooks = async () => {
    try {
      const { learnNow, subActive } = state;
      const { gradeId, termId } = learnNow;
      const subject = subjectList[subActive].key   
      // 获取教材版本列表
      const editionParams = {
        grade: gradeId,
        subject
      };
      
      const editionRes: any = await getEditionList_YxprApi(editionParams);
      
        // 获取教材书本列表
        const bookParams = {
          grade: gradeId,
          subject: subject,
          edition:idYxp.value
          // termId: termId
        };
      
      const bookRes: any = await getBookList_YxpApi(bookParams);
      
      const isEditionIdYxp = editionRes.data?.find(item => item.idYxp == query.editionId_yxp)
      if (!isEditionIdYxp.idYxp) { 
        editionRes.data = editionRes.data.splice(1, 0, {idYxp:query.editionId_yxp,name:query.editionName})
      }

      setData({
        editionList: editionRes.data || [],
        bookList: bookRes.data || [],
        filteredBookList: bookRes.data || [],
        bookModalLoading: false
      });
      
    } catch (error) {
      // console.error('获取教材数据失败:', error);
      setData({
        editionList: [],
        bookList: [],
        filteredBookList: [],
        bookModalLoading: false
      });
      // ElMessage.error('获取教材数据失败');
    }
  }

  // 选择教材版本
  const selectEdition = (editionId: string) => {
    idYxp.value = editionId

    getEditionListAndBooks()
    // 过滤书本列表
    if (editionId === '') {
      // 显示全部
      setData({
        filteredBookList: state.bookList
      });
    } else {
      // 根据版本idYxp过滤
      const filtered = state.bookList.filter((book: any) => 
        book.editionId == editionId
      );
      setData({
        filteredBookList: filtered
      });
    }
  }

  // 选择教材书本
  const selectBook = (book: any) => {
    // console.log(book,"bookbookbookbook")
    let bookName = `${book?.editionName}${book?.gradeName}${book?.termName}`

    // 更新当前选中的教材信息
    setData({
      bookName: bookName,
      showBookModal: false,
      currentBookId: book.bookId // 更新当前选中的教材ID
    });

    // 保存选中的教材信息到localStorage
    try {
      // 从路由中获取更多科目信息
      const subjectType = query.subjectType || '';
      const courseTitle = query.courseTitle || '';
      const moduleType = query.moduleType || '1';

      // 构建要保存的教材信息对象，添加更多科目相关信息
      const bookInfo = {
        bookId: book.bookId,
        bookName: bookName,
        editionId: book.editionId,
        editionName: book.editionName,
        gradeName: book.gradeName,
        gradeId: state.learnNow.gradeId, // 使用state.learnNow获取年级ID
        termName: book.termName,
        subject: book.subject || route.query.subject,
        subjectType: subjectType,
        courseTitle: courseTitle,
        moduleType: moduleType
      };
      
      // 构建缓存键名，使用科目类型和课程标题来区分不同科目的缓存
      const cacheKey = `syncSelectedBook_${subjectType}`;
      
      // 保存到localStorage
      localStorage.setItem(cacheKey, JSON.stringify(bookInfo));
      
      // 同时保存一个默认的，用于向后兼容
      localStorage.setItem('syncSelectedBook', JSON.stringify(bookInfo));
    } catch (err) {
      console.error('保存教材信息失败:', err);
    }

    // 更新URL参数
    const newQuery = {
      ...route.query,
      bookId: book.bookId 
    };
    
    // router.replace({
    //   name: route.name,
    //   query: newQuery
    // });
    
    // 重新获取章节列表
    getCourseList(book.bookId);
    
    ElMessage.success('教材切换成功');
  }

  // 处理图片加载错误
  const handleImageError = (event: any) => {
    event.target.src = '/src/assets/img/user/nodata.png';
  }

  // 关闭切换教材弹窗
  const closeBookModal = () => {
    idYxp.value =''
    setData({
      showBookModal: false,
      selectedEdition: '', // 重置为默认状态
      editionList: [],
      bookList: [],
      filteredBookList: [],
      bookModalLoading: false
      // 不重置currentBookId，保持当前选中状态
    });
  }

  // 知识点例题-跳转视频
const wekePlay2 = (data,preData) => {
    if (getIsMember()) {
      // const { i, i2, i3 } = e.currentTarget.dataset
      const { subActive, chapterId } = state
      // let info2 = data[i].xypVideos[i2]
      // const info = info2.videoList[i3]
      // const { type } = info2
      // const { videoId } = info
      const subject = subjectList[subActive].key

      // 保存当前滚动位置和章节ID到sessionStorage
      sessionStorage.setItem('syncListScrollTop', state.scrollTop.toString());
      sessionStorage.setItem('syncListScrollTop2', state.scrollTop2.toString());
      sessionStorage.setItem('syncListChapterId', chapterId);
      
      const query: any = route.query;
      query.vid = data?.videoId
      query.type = data?.type
      query.source = data?.source
      query.subject = subject
      query.id = chapterId
      router.push({ name: "SynchronousVideo", query:query })
    }
  }

  const goBack = () => {
    // Check if we came from a specific page that we should return to
    // const referrer = document.referrer;
    // const fromVideoPage = route.query.fromVideoPage === 'true';
    
    // if (fromVideoPage) {
    //   // If we came from the video page, go back to the previous page
      router.go(-1);
    // } else {
    //   // Otherwise, go to the index page
    //   router.push({ name: "SynchronousIndex" });
    // }
  };

  // 设置tabs科目枚举
  const setSubList = () => {
    const { sortArr, sortArr2, learnNow, isFirst } = state
    const { gradeId } = learnNow
    if ([1, 2, 3, 4, 5, 6].includes(gradeId)) {
      // 小学
      const list = subjectEnum2
      //学科英文匹配数字key
      for (const i of list) {
        i['key2'] = subjectList[i.key].key
      }
      //学科排序
      const list2 : any = []
      for (const x of sortArr2) {
        for (const y of list) {
          if (x == y.text) {
            list2.push(y)
          }
        }
      }
      setData({
        subList: list,
        subActive: 'chinese3'
      })
    
      //匹配页面传值的学科
      let name = state.subName
 
      const curSubData = list.find(item => item.key==query.subject)

      setData({
        subActive: query?.subject || curSubData?.key,
        subName:curSubData?.text
      })
      // if (isFirst && name) {
      //   for (let i of list2) {
      //     if (i.text == name) {
      //       setData({
      //         subActive: i.key,
      //         isFirst: 0
      //       })
      //       break
      //     }
      //   }
      // }
    } else {
      // 高中或初中（7年级无化学）
      const isJunior = [7, 8, 9].includes(gradeId)
      const cList = deepClone(subjectEnum)
      //追加初中科学
      cList.push({
        key: 'science',
        text: '科学'
      })
      const list = cList.map((item : any) => {
        item.key = item.key + (isJunior ? '' : '2')
        return item
      })
      //学科英文匹配数字key
      for (const i of list) {
        i.key2 = subjectList[i.key].key
      }
      //学科排序
      const list2 : any = []
      for (const x of sortArr) {
        for (const y of list) {
          if (x == y.text) {
            if (!(gradeId == 7 && x == '化学')) {
              list2.push(y)
            }
          }
        }
      }
      const curSubData = list.find(item => query.subject.includes(item.key))

      setData({
        subList: list,
        subActive: curSubData?.key,
        subName: curSubData?.text
      })
      //匹配页面传值的学科
      // let name = state.subName
      // if (isFirst && name) {
      //   for (let i of list2) {
      //     if (i.text == name) {
      //       setData({
      //         subActive: i.key,
      //         isFirst: 0
      //       })
      //       break
      //     }
      //   }
      // }
    }
    getBookVersion()
  }
  // 查询指定教材版本，并设定默认学期的数据
  const getBookVersion = () => {
    const { learnNow, subActive, sessionObject, termNameNow, subObject } = state
    const { versions } = learnNow

    const user : any = versions.find((item : any) => {
      return item.subject.includes(subActive)
    })

    // 检查 user 是否存在，避免访问 undefined 的属性  todo zzh
    if (!user) {
      ElMessage.error("当前年级没有该科目教材，请更换年级再来吧！")
      setData({
        isShowContent: false
      })
      return
    }
    
    //记录学习学科
    const subName = user.subjectName
    setLearnKey(subName)
    setData({
      subName
    })
    const isReq = !sessionObject[subActive]
    if (isReq) {
      // 没有请求过数据
      const obj = deepClone(sessionObject)
      const obj2 = deepClone(subObject)
      if (user) {
        const list : any = []
        list.push(user)
        if (!list.length) {
          ElMessage.error('当前年级没有该科目教材，请更换年级再来吧！')
          setData({
            isShowContent: false
          })
          return
        }
        const subjectNow = list.find((item : any) => {
          if (!item.termName) {
            //null改成全年制
            return true
          } else if (item.termName != termNameNow) {
            //如果上下学期不一致
            return item
          } else {
            return item.termName === termNameNow
          }
        })
        obj[subActive] = list
        obj2[subActive] = subjectNow
        setData({
          sessionObject: obj,
          subObject: obj2,
          isShowContent: true
        })
        setBookName()
      } else {
        ElMessage.error("当前年级没有该科目教材，请更换年级再来吧！")
        setData({
          isShowContent: false
        })
      }
    } else {
      setBookName()
    }
  }
  //设置当前教材
  const setBookName = () => {
    const { subObject, subActive, learnNow } = state
    const { gradeId, gradeName } = learnNow
    const { editionName, name, typeName, termName } = subObject[subActive]
    let bookName = ``
    if (query.source == 'historyTask') {
      bookName=editionName + (typeName || '') + gradeName + (termName || '全年制')
    } else { 
      bookName = `${query?.editionName}${query?.gradeName}${query?.termName}`
    }
 
    // 检查localStorage中是否有保存的教材信息
    let savedBookInfo:any = null;
    try {
      // 获取当前科目和模块类型
      const subjectType = query.subjectType || '';
      const moduleType = query.moduleType || '1';
      
      // 先尝试获取针对特定科目的缓存
      const cacheKey = `syncSelectedBook_${subjectType}`;
      let savedBookInfoStr = localStorage.getItem(cacheKey);
      
      if (savedBookInfoStr) {
        savedBookInfo = JSON.parse(savedBookInfoStr);
        
        // 检查年级是否匹配，如果不匹配则清除该科目的缓存
        if (savedBookInfo.gradeId && savedBookInfo.gradeId !== gradeId) {
          // console.log('年级不匹配，清除缓存');
          localStorage.removeItem(cacheKey);
          localStorage.removeItem('syncSelectedBook');
          savedBookInfo = null;
        }
      }
    } catch (err) {
      console.error('读取保存的教材信息失败:', err);
    }

    // 如果URL中没有bookId参数，但localStorage中有保存的教材信息，则使用保存的信息
    if (savedBookInfo) {
      // 更新query对象，加入保存的教材信息
      query.bookId = savedBookInfo.bookId;
      // query.courseTitle = savedBookInfo.courseTitle;
      
      // 如果有其他需要的参数也一并添加
      if (savedBookInfo.subject) {
        query.subject = savedBookInfo.subject;
      }
      if (savedBookInfo.editionName) {
        query.editionName = savedBookInfo.editionName;
      }
      
      bookName = `${savedBookInfo?.editionName}${savedBookInfo?.gradeName}${savedBookInfo?.termName}`
      // console.log("savedBookInfo ---", bookName, savedBookInfo)
      setData({
        currentBookId: savedBookInfo.bookId // 更新当前选中的教材ID
      });
    }
    //小学初中显示上下学期
    // const bookName1 = editionName + (typeName || '') + gradeName + (termName || '全年制')
    // //高中用name,去除教材同名为空
    // const bookName2 = editionName + gradeName + (name?.replace(editionName, '') || '')
    // if (gradeId > 9) {
    //   bookName = bookName2
    // } else {
    //   bookName = bookName1
    // }
    setData({
      bookName
    })
    if(savedBookInfo?.bookId){
      getCourseList(savedBookInfo.bookId)
    }else{
      getCourseList()
    }
  }

  // 检查字符串是否包含空的<sup>标签
  // 增强版的ReplaceMathString函数已经在上面定义

  // 使用从htmlCleaner导入的enhanceStringProcessor函数和cleanMenuText函数
</script>

<style lang="scss" scoped>
  .none {
    display: none !important
  }

  .nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .content {
    width: 100%;
    overflow-y: auto;
    background: #F5F5F5;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
  }

  .wrap {
    float: left;
    width: 100%;
  }

  /* 章节 */
  .menu_lt {
    float: left;
    width: 17.75rem;
    height: calc(100vh - 7.5rem);
    overflow-y: auto;
    border: .0625rem solid #eaeaea;
    border-bottom: 0;
    background: #ffffff;
    display: flex;
    flex-flow: column;
  }

  .subbox {
    float: left;
    width: 100%;
    box-sizing: border-box;
    padding: 1.25rem .875rem;
  }

  .subkey {
    width: 100%;
  }

  .subkey :deep(.el-select__wrapper) {
    height: 2.0625rem;
    border-radius: 1.375rem;
    background: #ffffff;
    box-shadow: 0 0 0 .0625rem #00c9a3 inset;
    font-size: 1rem;
  }

  .subkey :deep(.el-select__selection) {
    position: relative;
    top: -0.0625rem;
  }

  :deep(.subkey .el-select__icon svg) {
    display: none;
  }

  :deep(.subkey .el-select__icon) {
    background: url(@/assets/img/teachroom/down2.svg) no-repeat;
    background-size: .875rem .5625rem;
    background-position: center center;
  }

  /* select */
  .el-select-dropdown__item.selected {
    color: #00C9A3 !important;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover,
  .el-select-dropdown__item.is-hovering {
    color: #00C9A3 !important;
    background: #e5f9f6 !important;
  }

  .el-select-dropdown {
    box-shadow: 0 .125rem 1.25rem 0 #00000040 !important;
  }

  .el-select-dropdown__item {
    height: 3.0625rem !important;
    line-height: 3.0625rem !important;
  }

  .menu_ul {
    flex: 1;
    overflow-y: auto;
    // padding:1rem 0 .875rem;
    padding: 15px;
  }

  .menu_ul div {
    float: left;
  }

  .menu_li {
    width: 100%;
  }

  .menu1 {
    width: 100%;
    line-height: 2.5625rem;
    border-left: .1875rem solid #f5f5f5;
    background: #f5f5f5;
    margin: 0 0 1.125rem;
    display: flex;
    align-items: center;
    position: relative;
  }

  .menu1.active {
    border-left: .1875rem solid #00c9a3;
  }

  .menu1 div {
    flex: 1;
    line-height: 2.5625rem;
    color: #2a2b2a;
    font-size: 1rem;
    margin: 0 0 0 .4375rem;
  }

  .menu_img {
    float: right;
    width: .875rem;
    height: .8125rem;
    margin: 0 .625rem 0 0;
  }

  .menu_img.up {
    transform: rotate(-180deg);
  }

  .child {
    width: 100%;
  }

  .menu2 {
    width: 100%;
    margin: 0 0 1.125rem;
    display: flex;
    align-items: center;
    position: relative; /* 添加相对定位 */
  }
  .menu2:hover{
    background: #e9e9e9;
  }

  .menu2 div {
    flex: 1;
    line-height: 2.5625rem;
    color: #666;
    font-size: 1rem;
    margin: 0 0 0 1.875rem;
  }

  .menu2.active {
    background: #e5f9f6;
  }

  .menu2.active div {
    color: #009c7f;
  }

  .menu3 {
    width: 100%;
  }

  .menu3 div {
    width: 100%;
    line-height: 2.5625rem;
    color: #666;
    font-size: 1rem;
    box-sizing: border-box;
    // padding: 0 .3125rem 0 3.125rem;
    margin: 0 0 1.125rem;
  }

  .menu3-item {
    width: 100%;
    line-height: 2.5625rem;
    color: #666;
    font-size: 1rem;
    box-sizing: border-box;
    padding: 0 .3125rem 0 3.125rem;
    margin: 0 0 1.125rem;
    position: relative;
    display: flex;
    align-items: center;
  }

  .menu3-item.active {
    color: #009c7f;
    background: #e5f9f6;
  }

  .menu3-item:hover {
    background: #e9e9e9;
  }

  .menu3 div.active {
    color: #009c7f;
    background: #e5f9f6;
  }

  .menu1:hover,
  .menu2:hover,
  .menu3-item:hover {
    cursor: pointer;
  }

  /* 当前教材 */
  .menu_rt {
    float: right;
    width: 62.5rem;
    height: calc(100vh - 7.5rem);
    overflow-y: auto;
    margin: 0 0 0 .625rem;
    display: flex;
    flex-flow: column;
  }

  .book {
    width: 100%;
    height: 5rem;
    border: .0625rem solid #eaeaea;
    background: #ffffff;
    box-sizing: border-box;
    padding: 1.375rem 1.5625rem;
  }

  .book_ver {
    float: left;
    line-height: 2.25rem;
    color: #2a2b2a;
    font-size: 1rem;
    display: flex;
    align-items: center;
  }

  .book_ver span {
    font-weight: bold;
  }

  .book_fav {
    float: right;
    width: 6.875rem;
    height: 2.25rem;
    border-radius: .25rem;
    border: .0625rem solid #009c7f;
    color: #009c7f;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .book_fav img {
    width: 1rem;
    height: 1rem;
    margin: 0 .375rem 0 0;
  }

  /* 知识点 */
  .wk_box {
    width: 100%;
  }
  .wk_h1 {
    width: 57.625rem;
    box-sizing: border-box;
    padding: 1.25rem 0 .625rem;
    line-height: 1.3125rem;
    color: #009c7f;
    font-size: 1rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    border-bottom: .0625rem dashed #eaeaea;
  }

  .wk_h1 span:first-child {
    float: left;
    width: .875rem;
    height: 1rem;
    border-radius: 0 .375rem .375rem 0;
    background: #5a85ec;
    margin: 0 .6875rem 0 0;
  }

  .wk_ul {
    width: 100%;
    margin: .625rem 0 0;
    display: flex;
    flex-wrap: wrap;
  }

  .wk_li {
    width: 24.2%;
    border-radius: .625rem;
    margin: 0 1% 1.25rem 0;
    position: relative;
    // overflow: hidden;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .task-badge{
      position: absolute;
      left: 0px;
      top: -20px;
      width: 38px;
      height: 24px;
      z-index: 1;
    }
  }

  .wk_li:nth-child(4n+4) {
    margin-right: 0;
  }
  .wk_pic {
    position: relative;
    // float: left;
    width: 100%;
    height: 8.5625rem;

  }

  .wk_img {
    // float: left;
    width: 100%;
    height: 8.5625rem;
    // border-radius: .625rem .625rem 0 0;
    object-fit: cover;
    object-position: center;
    border-radius: .625rem .625rem 0 0;
    overflow: hidden;
  }

  .view{
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 0 8px;
    line-height: 24px;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    border-radius: 10px;
    font-size: 12px;
    text-decoration: underline;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    backdrop-filter: blur(2px);
  }

  .view:hover {
    background: rgba(0, 156, 127, 0.9);
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .wk_play {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 1.5rem;
    height: 1.5rem;
    z-index: 8;
    transition: transform 0.2s ease;
  }
  
  .wk_play:hover {
    transform: scale(1.2);
    cursor: pointer;
  }

  .wk_bom {
    width: 100%;
    box-sizing: border-box;
    padding: .625rem;
    background: #fff;
  }

  .wk_tit {
    width: 100%;
    line-height: 1.1875rem;
    color: #2a2b2a;
    font-size: .875rem;
  }

  .wk_state {
    width: 100%;
    margin: 1rem 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .wk_point {
    width: 100%;
    line-height: 2rem;
    text-align: center;
    border-radius: .25rem;
    background: #e5f9f6;
    color: #009c7f;
    font-size: .875rem;
    margin: .625rem 0 0;
  }

  .wk_status {
    width: 3.75rem;
    height: 1.5rem;
    line-height: 1.5rem;
    text-align: center;
    border-radius: 0 .75rem .75rem 0;
    color: #ffffff;
    font-size: .75rem;
    position: relative;
    left: -0.625rem;
  }

  .status0 {
    background: #999999;
  }

  .status1 {
    background: linear-gradient(150.8deg, #f6d22b 0%, #f29500 100%);
  }

  .status2 {
    background: linear-gradient(166.7deg, #08d8b8 0%, #00b392 100%);
  }

  div.wk_collect {
    float: right;
    margin: 0 1.875rem 0 0;
  }

  .wk_collect img {
    width: 1.125rem;
    height: 1.125rem;
  }

  .wk_collect img:first-child,
  .wk_collect.active img:last-child {
    display: inline-block;
  }

  .wk_collect img:last-child,
  .wk_collect.active img:first-child {
    display: none;
  }

  div.wk_thumbs {
    line-height: 1.125rem;
    color: #666666;
    font-size: .75rem;
    float: right;
  }

  .wk_thumbs img {
    float: left;
    width: 1.125rem;
    height: 1.125rem;
    margin: 0 .375rem 0 0;
  }

  .wk_thumbs img:nth-child(1),
  .wk_thumbs.active img:nth-child(2) {
    display: inline-block;
  }

  .wk_thumbs img:nth-child(2),
  .wk_thumbs.active img:nth-child(1) {
    display: none;
  }

  .book_fav:hover,
  .wk_pic:hover,
  .wk_play:hover,
  .wk_tit:hover,
  .wk_thumbs img:hover,
  .wk_collect:hover,
  .wk_point:hover {
    cursor: pointer;
  }

  /* 语文同步学习特殊样式 */
  .chinese-sync-container {
    position: relative;
    width: 100%;
    height: 8.5625rem;
    border-radius: .625rem .625rem 0 0;
    overflow: hidden;
  }

  .chinese-sync-card {
    width: 100%;
    height: 8.5625rem;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    background: #e9e9e9;
    justify-content: center;
    position: relative;
    border-radius: .625rem .625rem 0 0; /* 与wk_li的圆角保持一致 */
    overflow: hidden;
  }

  .chinese-sync-title {
    color: #2a2b2a;
    font-size: 50px;
    font-weight: 600;
    text-align: center;
    width: 90px;
    height: 90px;
    line-height: 90px;
    max-width: 85%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    background-size: cover;
  }
  
  .english-sync-title{
    color: #2a2b2a;
    font-size: 14px;
    text-align: center;
    width: 209px;
    height: 50px;
    line-height: 50px;
    max-width: 85%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    background-size: cover;
  }

  /* 语文同步学习容器 */
  .chinese-sync-container {
    position: relative;
    width: 100%;
    height: 8.5625rem;
    border-radius: .625rem .625rem 0 0;
    overflow: hidden;
  }

  /* 语文同步学习样式下的查看详情按钮调整 */
  .chinese-sync-view {
    position: absolute;
    top: 5px;
    left: 5px;
    z-index: 20;
  }

  /* 语文同步学习样式下的播放按钮调整 */
  .chinese-sync-play {
    position: absolute;
    bottom: 10px;
    right: 10px;
    z-index: 20;
    width: 1.5rem;
    height: 1.5rem;
    margin: 0; /* 重置默认margin */
    transition: transform 0.2s ease;
  }
  
  .chinese-sync-play:hover {
    transform: scale(1.2);
  }

  /* 暂无数据 */
  .nodata {
    flex: 1;
    color: #999999;
    font-size: .875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
  }

  .nodata img {
    width: 7.4375rem;
    height: 8rem;
    margin: 0 0 .625rem;
  }

  /* 老师详情弹窗样式 */
  .teacher-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideIn {
    from {
      transform: translateY(-50px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  .teacher-modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    animation: slideIn 0.3s ease-out;
  }

  .teacher-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #eaeaea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
  }

  .teacher-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s;
  }

  .close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
  }

  .close-btn:active {
    transform: scale(0.95);
  }

  .teacher-modal-content {
    padding: 24px;
    display: flex;
    gap: 20px;
  }

  .teacher-avatar {
    flex-shrink: 0;
  }

  .teacher-avatar img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .teacher-info {
    flex: 1;
  }

  .teacher-info > div {
    margin-bottom: 16px;
    display: flex;
    align-items: flex-start;
  }

  .teacher-info label {
    font-weight: 600;
    color: #333;
    min-width: 80px;
    margin-right: 8px;
  }

  .teacher-info span {
    color: #666;
    flex: 1;
  }

  .teacher-intro {
    flex-direction: column !important;
    align-items: flex-start !important;
  }

  .teacher-intro p {
    margin: 8px 0 0 0;
    line-height: 1.6;
    color: #666;
    text-align: justify;
  }

  .teacher-stats {
    display: flex;
    justify-content: space-around;
    margin: 0 24px 24px 24px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    border-top: 2px solid #667eea;
  }

  .stats-item {
    text-align: center;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .stats-icon {
    font-size: 20px;
    margin-bottom: 8px;
  }

  .stats-number {
    font-size: 24px;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 4px;
  }

  .stats-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
  }

  /* 加载状态样式 */
  .teacher-modal-content.loading {
    justify-content: center;
    align-items: center;
    padding: 40px 24px;
  }

  .teacher-loading {
    text-align: center;
    color: #666;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .teacher-loading p {
    margin: 0;
    font-size: 16px;
  }

  /* 无数据状态样式 */
  .teacher-modal-content.no-data {
    justify-content: center;
    align-items: center;
    padding: 40px 24px;
  }

  .teacher-no-data {
    text-align: center;
    color: #999;
  }

  .no-data-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .teacher-no-data p {
    margin: 0;
    font-size: 16px;
  }

  /* 弹窗底部提示 */
  .teacher-modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #eaeaea;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
    text-align: center;
  }

  .modal-hint {
    margin: 0;
    font-size: 12px;
    color: #999;
    font-style: italic;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .teacher-modal {
      width: 95%;
      margin: 20px;
    }
    
    .teacher-modal-content {
      flex-direction: column;
      text-align: center;
      padding: 20px;
    }
    
    .teacher-avatar {
      align-self: center;
      margin-bottom: 20px;
    }

    .teacher-avatar img {
      width: 100px;
      height: 100px;
    }

    .teacher-info > div {
      justify-content: center;
      text-align: center;
    }

    .teacher-info label {
      min-width: 70px;
    }

    .teacher-stats {
      margin: 0 20px 20px 20px;
      padding: 15px;
    }

    .stats-icon {
      font-size: 18px;
      margin-bottom: 6px;
    }

    .stats-number {
      font-size: 20px;
    }

    .stats-label {
      font-size: 11px;
    }
  }

  /* 切换教材弹窗样式 */
  .book-modal-dialog {
    border-radius: 20px;
    height: 660px;
    .el-dialog {
      border-radius: 12px;
      overflow: hidden;
      height: 660px;
    }
    
    .el-dialog__body {
      padding: 0;
    }
  }

  .book-modal-content {
    padding: 20px;
  }

  .book-loading {
    text-align: center;
    padding: 60px 20px;
    color: #666;
  }

  .book-loading .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #009c7f;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
  }

  .book-loading p {
    margin: 0;
    font-size: 16px;
  }

  .edition-selector {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eaeaea;
  }

  .selector-label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
    white-space: nowrap;
  }

  .edition-select {
    flex: 1;
    width:100%;
  }

  .edition-select :deep(.el-select__wrapper) {
    height: 2.0625rem;
    border-radius: 1.375rem;
    background: #ffffff;
    box-shadow: 0 0 0 .0625rem #00c9a3 inset;
    font-size: 1rem;
  }

  .edition-select :deep(.el-select__selection) {
    position: relative;
    top: -0.0625rem;
  }

  :deep(.edition-select .el-select__icon svg) {
    display: none;
  }

  :deep(.edition-select .el-select__icon) {
    background: url(@/assets/img/teachroom/down2.svg) no-repeat;
    background-size: .875rem .5625rem;
    background-position: center center;
  }

  /* 教材版本下拉列表样式 */
  :deep(.edition-select .el-select-dropdown__item.selected) {
    color: #00C9A3 !important;
  }

  :deep(.edition-select .el-select-dropdown__item.hover),
  :deep(.edition-select .el-select-dropdown__item:hover),
  :deep(.edition-select .el-select-dropdown__item.is-hovering) {
    color: #00C9A3 !important;
    background: #e5f9f6 !important;
  }

  :deep(.edition-select .el-select-dropdown) {
    box-shadow: 0 .125rem 1.25rem 0 #00000040 !important;
  }

  :deep(.edition-select .el-select-dropdown__item) {
    height: 2.5rem !important;
    line-height: 2.5rem !important;
  }

  .book-list-container {
    max-height: 400px;
    overflow-y: auto;
  }

  .book-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(136px, 1fr));
    gap: 62px;
  }

  .book-item {
    border: 1px solid #eaeaea;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    position: relative;
    
    &:hover {
      // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
      // border-color: #009c7f;
    }
  }

  /* 当前选中教材样式 */
  .book-item-active {
    border: 2px solid #00c9a3;
    // box-shadow: 0 4px 12px rgba(0, 156, 127, 0.2);
    transform: translateY(-2px);
  }

  .current-book-badge {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 30%;
    left: 50%;
    transform: translateX(-50%);
    width: 108px;
    height: 28px;
    border-radius: 14.5px;
    background: rgba(0, 0, 0, 0.6);   
    color: white;
    // padding: 4px 8px;
    border-radius: 12px;
    font-size: 14px;
    z-index: 10;
  }

  .book-cover {
    width: 136px;
    height: 192px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .book-info {
    padding:6px 12px;
  }

  .book-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .book-desc {
    font-size: 12px;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .no-books {
    text-align: center;
    padding: 40px;
    color: #999;
    
    img {
      width: 100px;
      height: 100px;
      margin-bottom: 10px;
    }
    
    p {
      margin: 0;
      font-size: 14px;
    }
  }

  .book-modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #eaeaea;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
    text-align: center;
    
    .modal-hint {
      margin: 0;
      font-size: 12px;
      color: #999;
      font-style: italic;
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .book-modal-dialog {
      .el-dialog {
        width: 95% !important;
        margin: 20px;
      }
    }
    
    .book-grid {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 10px;
    }
    
    .edition-selector {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
    
    .edition-select {
      width: 100%;
      max-width: none;
    }

    /* 语文同步学习移动端优化 */
    .chinese-sync-title {
      font-size: 12px;
      padding: 6px 8px;
      max-width: 90%;
    }

    .chinese-sync-view {
      font-size: 10px;
      padding: 0 6px;
      line-height: 20px;
    }

    .chinese-sync-play {
      width: 1.25rem;
      height: 1.25rem;
      bottom: 8px;
      right: 8px;
    }

    /* 老师详情弹窗移动端适配 */
    .teacher-detail-dialog {
      .el-dialog {
        width: 95% !important;
        margin: 20px;
      }
    }

    .teacher-iframe {
      height: 500px !important;
    }

    .teacher-detail-content {
      min-height: 500px;
    }

    .teacher-loading {
      min-height: 500px;
    }

    .iframe-error {
      min-height: 500px;
    }

    /* 老师信息移动端优化 */
    .teacher-info-container {
      min-height: 400px;
      padding: 15px;
    }

    .teacher-info-wrapper {
      min-height: 370px;
    }

    .teacher-info-image {
      width: 100%;
      height: 100%;
    }

    .image-error-message {
      padding: 30px 20px;
    }

    .image-error-message p {
      font-size: 14px;
    }

    .image-error-message small {
      font-size: 11px;
    }
  }

  /* 老师详情弹窗样式 */
  .teacher-detail-dialog {
    .el-dialog {
      border-radius: 12px;
      overflow: hidden;
    }
    
    .el-dialog__body {
      padding: 0;
    }
  }

  .teacher-detail-content {
    min-height: 600px;
    background: #f8f9fa;
  }

  .teacher-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 600px;
    color: #666;
  }

  .teacher-loading .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #009c7f;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  .teacher-loading p {
    margin: 0;
    font-size: 16px;
  }

  .teacher-iframe-container {
    position: relative;
    width: 100%;
    min-height: 600px;
  }

  .teacher-iframe {
    border: none;
    border-radius: 0 0 12px 12px;
    background: white;
  }

  .iframe-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 600px;
    color: #999;
    background: white;
  }

  .iframe-error p {
    margin: 0 0 20px 0;
    font-size: 16px;
  }

  /* 老师信息容器样式 */
  .teacher-info-container {
    width: 100%;
    min-height: 500px;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
  }

  .teacher-info-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 460px;
  }

  .teacher-info-image {
    max-width: 100%;
    max-height: 460px;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
    cursor: zoom-in;
  }

  .teacher-info-image:hover {
    transform: scale(1.02);
  }

  /* 图片加载错误样式 */
  .image-error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    padding: 40px;
    text-align: center;
  }

  .image-error-message p {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 500;
  }

  .image-error-message small {
    margin: 0;
    font-size: 12px;
    color: #ccc;
  }

    .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
      .el-dialog {
    border-radius: 20px;
    overflow: hidden;
    // box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border: none;
    // background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
    position: relative;
  }
  
  .el-dialog__header {
    padding: 0;
    margin: 0;
    background: transparent;
    border-bottom: none;
  }
  
  .el-dialog__body {
    padding: 0;
    // background: transparent;
  }
    .dialog-title {
      display: flex;
      align-items: center;
      
      .title-text {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
      
      .teacher-name {
        font-size: 14px;
        font-weight: 400;
        color: #666;
        margin-left: 8px;
      }
    }
    
    .header-actions {
      display: flex;
      align-items: center;
    }
    
    .close-icon {
      position: absolute;
      right: 16px;
      top: 16px;
      z-index: 1000;
      font-size: 24px;
      color: #999;
      cursor: pointer;
      transition: color 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(4px);
      
      &:hover {
        color: #333;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
  .page-header {
  margin-bottom: 18px;margin-top: 20px;
}

.breadcrumbs {
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
}

.breadcrumbs .back-link {
  // color: #606266;
  text-decoration: none;
  color: #009c7f;
}
.breadcrumbs .back-link:hover {
  color: #00bfa5;
}

.breadcrumb-separator {
  color: #c0c4cc;
  margin: 0 5px;
}

.breadcrumb-item {
  color: #606266;
}
.breadcrumb-item.active {
  color: #303133;
  font-weight: 500;
}

/* 任务标签样式 */
.task-badge {
  position: absolute;
  left: -30px;
  top: -8px;
  width: 38px;
  height: 24px;
  z-index: 1;
  background: url('@/assets/img/synchronous/renwu.png') center center no-repeat;
  background-size: 100%;
}

/* 第一级菜单任务标签调整 */
.menu1 .task-badge {
  left: -35px;
  top: 50%;
  transform: translateY(-50%);
}

/* 第二级菜单任务标签调整 */
.menu2 .task-badge {
  left: -35px;
  top: 0;
  transform: translateY(-50%);
}

/* 第三级菜单任务标签调整 */
.menu3-item .task-badge {
  left: 0;
  top: 0;
  transform: translateY(-50%);
}

.task-img-container {
  position: absolute;
  left: -3px;
  top: -26px;
  z-index: 20;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none; /* Allow clicks to pass through to elements below */
  width: 45px;
  height: 35px;
  overflow: visible;
}

.task-img {
  width: 38px;
  height: 24px;
  object-fit: contain;
  filter: drop-shadow(0px 1px 2px rgba(0,0,0,0.3)); /* Add subtle shadow for better visibility */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .book-modal-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 20px;
    }
  }
  
  .book-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
  }
  
  .edition-selector {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .edition-select {
    width: 100%;
    max-width: none;
  }

  /* 语文同步学习移动端优化 */
  .chinese-sync-title {
    font-size: 12px;
    padding: 6px 8px;
    max-width: 90%;
  }

  .chinese-sync-view {
    font-size: 10px;
    padding: 0 6px;
    line-height: 20px;
  }

  .chinese-sync-play {
    width: 1.25rem;
    height: 1.25rem;
    bottom: 8px;
    right: 8px;
  }

  /* 老师详情弹窗移动端适配 */
  .teacher-detail-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 20px;
    }
  }

  .teacher-iframe {
    height: 500px !important;
  }

  .teacher-detail-content {
    min-height: 500px;
  }

  .teacher-loading {
    min-height: 500px;
  }

  .iframe-error {
    min-height: 500px;
  }

  /* 老师信息移动端优化 */
  .teacher-info-container {
    min-height: 400px;
    padding: 15px;
  }

  .teacher-info-wrapper {
    min-height: 370px;
  }

  .teacher-info-image {
    width: 100%;
    height: 100%;
  }

  .image-error-message {
    padding: 30px 20px;
  }

  .image-error-message p {
    font-size: 14px;
  }

  .image-error-message small {
    font-size: 11px;
  }
}
</style>
