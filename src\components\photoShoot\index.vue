<template>
    <div class="camera-container">
        <!-- 拍照按钮 -->
        <div class="upload-btn" v-if="!videoState.isShoot" @click="openVideo">拍照上传（{{ length }}/5）</div>
    </div>
    <el-dialog
        v-model="videoState.dialogVisible"
        title="拍照"
        width="1000"
        @close="closeVideo"
        :close-on-click-modal="false"
    >
        <div v-if="hasCamera" v-loading="loading" class="center-box">
            <!-- 视频预览 -->
            <div v-if="!photo">
                <video ref="video" autoplay playsinline class="video-preview"></video>
            </div>
            <!-- 裁剪组件 -->
            <div v-if="photo" class="cropper-container">
                <vue-cropper
                ref="cropper"
                :src="photo"
                :aspect-ratio="NaN"
                :drag-mode="'move'"
                :view-mode="2"
                guides
                ></vue-cropper>
            </div>
            <div style="display: flex; justify-content: center; align-items: center;">
                <button v-if="!photo" @click="capture" class="capture-btn">拍照</button>
                <div v-else>
                    <button @click="reShoot">重新拍照</button>
                    <button @click="rotateImage(-90)">↺ 左转</button>
                    <button @click="rotateImage(90)">↻ 右转</button>

                    <button @click="crop">裁剪</button>
                </div>
            </div>
        </div>
        <div v-else style="height: 6.25rem;line-height: 6.25rem; text-align: center;">
            摄像头打开失败，请检查是否正确接入摄像头
        </div>
    </el-dialog>
</template>
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import VueCropper from 'vue-cropperjs'
import 'cropperjs/dist/cropper.css'
import { uploadApi } from "@/api/user"

const props = defineProps({
  length: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['setImg'])

const video = ref<HTMLVideoElement | null>(null)
const photo = ref('')
const croppedPhoto = ref('')
const showCropper = ref(false)
const loading = ref(false)
const hasCamera = ref(true)
const cropper = ref<any>(null)
const videoState = reactive({
  dialogVisible: false, 
  isShoot: false
})
let stream: MediaStream | null = null

const openVideo = () => {
  videoState.dialogVisible = true
  startCamera()
}

const closeVideo = () => {
  videoState.dialogVisible = false
  hasCamera.value = true
  photo.value = ''
  if (stream) {
    stream.getTracks().forEach(track => track.stop())
  }
}

const startCamera = async () => {
  try {
    loading.value = true
    stream = await navigator.mediaDevices.getUserMedia({
      video: { width: 1280, height: 720, facingMode: 'environment' },
      audio: false
    })
    if (video.value) {
      video.value.srcObject = stream
    }
    loading.value = false
    hasCamera.value = true
  } catch (err) {
    hasCamera.value = false
    loading.value = false
    console.error('摄像头访问失败:', err)
  }
}
// 旋转图片
const rotateImage = (degrees: number) => {
  if (cropper.value) {
    cropper.value.rotate(degrees)
  }
}

const reShoot = () => {
  photo.value = ""
  startCamera()
}

const capture = () => {
  if (!video.value) return
  
  const canvas = document.createElement('canvas')
  canvas.width = video.value.videoWidth
  canvas.height = video.value.videoHeight
  const ctx = canvas.getContext('2d')
  if (stream) {
    stream.getTracks().forEach(track => track.stop())
  }
  if (ctx) {
       // 旋转180度的正确方法
       ctx.save()
    // 移动到画布中心
    ctx.translate(canvas.width / 2, canvas.height / 2)
    // 旋转180度
    ctx.rotate(Math.PI)
    // 绘制图像（注意坐标需要调整）
    ctx.drawImage(video.value, -canvas.width / 2, -canvas.height / 2, canvas.width, canvas.height)
    ctx.restore()
    photo.value = canvas.toDataURL('image/png')
    showCropper.value = true
  }
}

const crop = () => {
  if (cropper.value) {
    const croppedCanvas = cropper.value.getCroppedCanvas()
    croppedPhoto.value = croppedCanvas.toDataURL('image/png')
    showCropper.value = false
  }
  upload()
  closeVideo()
}

const upload = async () => {
  if (!croppedPhoto.value) return
  
  try {
    const file = base64ToFile(croppedPhoto.value, Math.random() + 'image.png')
    const formdata = new FormData()
    formdata.append("file", file)
    const res = await uploadApi(formdata) as any
    const { data = {} } = res
    emit('setImg', data)
  } catch (err) {
    console.error('上传失败:', err)
  }
}

const base64ToFile = (base64: string, filename: string) => {
  const arr = base64.split(',')
  const mime = arr[0]?.match(/:(.*?);/)![1]
  const bstr = atob(arr[1]?arr[1]:'')
  
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  
  return new File([u8arr], filename, { type: mime })
}
</script>

<style scoped>
.video-preview, .photo-preview {
    max-width: 100%;
    height: auto;
    display: block;
    margin: .625rem 0;
}

.cropper-container {
    width: 50rem;
    height: 31.25rem;
    margin: 1.25rem 0;
}

button {
    margin: .3125rem;
    padding: .5rem 1rem;
    background: #42b983;
    color: white;
    border: none;
    border-radius: .25rem;
    cursor: pointer;
}

button:hover {
    background: #369f6b;
}
.photo1 {
    width: 50rem;
    height: 31.25rem;
}
.upload-btn {
    width: 8.25rem;
    padding: .375rem .825rem;
    border-radius: .25rem;
    border: .0625rem solid #dddddd;
    background: #ffffff;
    color: #666666;
    text-align: center;
    font-size: .875rem;
    font-weight: 400;
    cursor: pointer;
    margin-bottom: .625rem;
}
.camera-container {
    display: flex;
    flex-direction: row-reverse;
    padding-right: 12.5rem;
}
.center-box {
    display: flex;
    align-items: center;
    flex-direction: column;
}
:deep(.cropper-modal) {
    cursor: pointer;
}

:deep(.cropper-container) {
    cursor: move;
}
</style>